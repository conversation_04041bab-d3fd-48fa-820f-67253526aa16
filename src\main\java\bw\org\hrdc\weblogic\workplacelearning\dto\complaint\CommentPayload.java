package bw.org.hrdc.weblogic.workplacelearning.dto.complaint;

import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.Comment;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.CommentDocument;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums.ComplaintStatus;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 28/03/25 12:08
 * @UpdatedBy martinspectre
 * @UpdatedOn 28/03/25 12:08
 */
@Data
public class CommentPayload {
    Comment comment;
    ComplaintStatus complaintStatus;
    List<CommentDocument> documents;
}
