package bw.org.hrdc.weblogic.workplacelearning.util.converters;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.CourseContentDeliveryJson;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @CreatedOn 14/04/25 10:10
 * @UpdatedBy martinspectre
 * @UpdatedOn 14/04/25 10:10
 */
public class CourseContentDeliveryConverter implements AttributeConverter<CourseContentDeliveryJson, String> {

    private static final Logger log = LoggerFactory.getLogger(CourseContentDeliveryConverter.class);

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String convertToDatabaseColumn(CourseContentDeliveryJson attribute) {
        try {
            return objectMapper.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            log.error("CourseContentDeliveryConverter convertToDatabaseColumn JSON writing error", e.getMessage());
            throw new RuntimeException("Failed to serialize ShortCourseInformation", e);
        }
    }

    @Override
    public CourseContentDeliveryJson convertToEntityAttribute(String dbData) {
        try {
            return objectMapper.readValue(dbData, CourseContentDeliveryJson.class);
        } catch (Exception e) {
            log.error("CourseContentDeliveryConverter convertToEntityAttribute JSON writing error", e.getMessage());
            throw new RuntimeException("Failed to deserialize ShortCourseInformation", e);
        }
    }
}
