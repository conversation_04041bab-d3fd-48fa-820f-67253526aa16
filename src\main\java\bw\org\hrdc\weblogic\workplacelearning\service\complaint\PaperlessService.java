package bw.org.hrdc.weblogic.workplacelearning.service.complaint;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.IOException;

/**
 * <AUTHOR>
 * @CreatedOn 27/03/25 22:47
 * @UpdatedBy martinspectre
 * @UpdatedOn 27/03/25 22:47
 */
@Service
@RequiredArgsConstructor
public class PaperlessService {

    private final WebClient webClient;

    @Value("${paperless.url}")
    private String paperlessUrl;

    @Value("${paperless.apiToken}")
    private String paperlessToken;

    public String uploadToPaperless(MultipartFile file) throws IOException {
        MultipartBodyBuilder bodyBuilder = new MultipartBodyBuilder();
        bodyBuilder.part("document", file.getResource());

        JsonNode response = webClient.post()
                .uri(paperlessUrl)
                .header(HttpHeaders.AUTHORIZATION, "Token " + paperlessToken)
                .contentType(org.springframework.http.MediaType.valueOf(MediaType.MULTIPART_FORM_DATA))
                .body(BodyInserters.fromMultipartData(bodyBuilder.build()))
                .retrieve()
                .bodyToMono(JsonNode.class)
                .block();

        if (response != null && response.has("id")) {
            return response.get("id").asText();
        } else {
            throw new RuntimeException("Failed to upload document to Paperless");
        }
    }
}

