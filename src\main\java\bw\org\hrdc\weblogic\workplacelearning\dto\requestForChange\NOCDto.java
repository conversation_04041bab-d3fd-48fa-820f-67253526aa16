package bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.dto.document.FileDocumentDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.CourseContentDeliveryJson;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ScopeOfAccreditation;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ShortCourseInformation;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
public class NOCDto {
    private String referenceNumber;
    private String applicationNumber;
    private String recognitionNumber;
    private String organisationId;
    private String trainingNeedsAssessmentPurpose;
    private String trainingNeedsAssessmentSkillsNeedsAnalysis;
    private String shortCourseDeliveryMode;
    private String keyFacilitation;
    private String assessmentType;
    private String certification;
    private String thirdPartyArrangements;
    private String resources;
    private String shortCourseEndorsement;
    private String justification;
    private LocalDateTime dateSubmitted = LocalDateTime.now();
    private Enums.Status applicationStatus;
    private Enums.State applicationState = Enums.State.DRAFT;
    private Enums.ChangeSeverity severity;
    private boolean merged = false;
    private Boolean isMajorChange = false;
    private ShortCourseInformation shortCourseInformation;
    private CourseContentDeliveryJson courseContentAndDelivery;
    private Set<ScopeOfAccreditation> scopeOfAccreditation;
    private String processInstanceId;
    private List<FileDocumentDto> attachments;
}
