package bw.org.hrdc.weblogic.workplacelearning.entity.complaint;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Auditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @CreatedOn 27/03/25 22:00
 * @UpdatedBy martinspectre
 * @UpdatedOn 27/03/25 22:00
 */
@Entity
@Table(name = "comment_documents")
@Getter @Setter
@NoArgsConstructor @AllArgsConstructor
public class CommentDocument extends Auditable implements Serializable {

    @ManyToOne
    @JoinColumn(name = "comment_id", nullable = false)
    @JsonIgnore
    private Comment comment;

    private String key;
    private String docName;
    private String docExt;
    private String identifier;
    private String fileType;
    private Integer docSize;
    private String fileUrl;
}
