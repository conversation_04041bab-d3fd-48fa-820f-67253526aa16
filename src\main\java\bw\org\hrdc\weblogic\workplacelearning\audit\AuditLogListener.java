package bw.org.hrdc.weblogic.workplacelearning.audit;

import bw.org.hrdc.weblogic.workplacelearning.dto.audit.AuditTrailDTO;
import bw.org.hrdc.weblogic.workplacelearning.service.audit.AuditLogService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApplicationContextProvider;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import org.hibernate.event.spi.*;
import org.hibernate.persister.entity.EntityPersister;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 * @CreatedOn 25/02/25 21:36
 * @UpdatedBy martinspectre
 * @UpdatedOn 25/02/25 21:36
 */
//TODO remove logging here
@Component
public class AuditLogListener implements PostInsertEventListener, PostUpdateEventListener, PostDeleteEventListener {
    private static final Logger logger = LoggerFactory.getLogger(AuditLogListener.class);
    @Override
    public void onPostInsert(PostInsertEvent event) {
        Object entity = event.getEntity();
        if (entity instanceof AuditAware) {
            List<AuditTrailDTO> auditTrailDTOList = new ArrayList<>();
            AuditLogService auditLogService = (AuditLogService) ApplicationContextProvider.getApplicationContext().getBean("auditLogService");
            String[] propertyNames = event.getPersister().getPropertyNames();
            Object[] states = event.getState();
            for (int i = 0; i < propertyNames.length; i++) {
                logger.info("Inside On Save   ************    ************** ===>>>      {}", propertyNames[i]);
                auditTrailDTOList.add(new AuditTrailDTO(entity.getClass().getCanonicalName(), event.getId().toString(), Enums.AuditEvent.INSERT.name(), propertyNames[i], null, states[i].toString()));
            }
            auditTrailDTOList.forEach(auditLogService::save);
        }
    }

    @Override
    public void onPostUpdate(PostUpdateEvent event) {
        Object entity = event.getEntity();
        if (entity instanceof AuditAware) {
            String[] propertyNames = event.getPersister().getPropertyNames();
            Object[] currentState = event.getState();
            Object[] previousState = event.getOldState();
            List<AuditTrailDTO> auditTrailDTOList = new ArrayList<>();
            AuditLogService auditLogService = (AuditLogService) ApplicationContextProvider.getApplicationContext().getBean("auditLogService");
            for (int i = 0; i < currentState.length; i++) {
                if (!previousState[i].equals(currentState[i])) {
                    logger.info("Inside On Flush Dirty   ************    **************      ==>>    {}", propertyNames[i]);
                    auditTrailDTOList.add(new AuditTrailDTO(entity.getClass().getCanonicalName(), event.getId().toString(), Enums.AuditEvent.UPDATE.name(), propertyNames[i], previousState[i].toString(), currentState[i].toString()));
                }
            }
            auditTrailDTOList.forEach(auditLogService::save);
        }
    }

    @Override
    public void onPostDelete(PostDeleteEvent event) {
        Object entity = event.getEntity();
        if (entity instanceof AuditAware) {
            String[] propertyNames = event.getPersister().getPropertyNames();
            Object[] state = event.getDeletedState();
            List<AuditTrailDTO> auditTrailDTOList = new ArrayList<>();
            AuditLogService auditLogService = (AuditLogService) ApplicationContextProvider.getApplicationContext().getBean("auditLogService");
            for (int i = 0; i < propertyNames.length; i++) {
                logger.info("Inside On Delete   ************    ************** ===>>>      {}", propertyNames[i]);
                auditTrailDTOList.add(new AuditTrailDTO(entity.getClass().getCanonicalName(), event.getId().toString(), Enums.AuditEvent.DELETE.name(), propertyNames[i], state[i].toString(), null));
            }
            auditTrailDTOList.forEach(auditLogService::save);
        }
    }

    @Override
    public boolean requiresPostCommitHandling(EntityPersister persister) {
        return false;
    }
}
