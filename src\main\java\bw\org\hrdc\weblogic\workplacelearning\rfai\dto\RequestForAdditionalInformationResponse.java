package bw.org.hrdc.weblogic.workplacelearning.rfai.dto;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
@Data
public class RequestForAdditionalInformationResponse {
    private Long id;
    private String organisationId;
    private String submittedByUserId;
    private LocalDateTime dateSubmitted;
    private ApplicationStatus status;
    private EtpDetailsDTO etpDetails;
    private List<AdditionalInformationResponse> additionalInformationRequired;
}
