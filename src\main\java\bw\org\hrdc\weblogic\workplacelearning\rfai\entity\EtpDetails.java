package bw.org.hrdc.weblogic.workplacelearning.rfai.entity;

import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Data;

@Embeddable
@Data
public class EtpDetails {
    private String nameOfEducationProvider;
    private String registrationAccreditationNumber;
    private String courseTitle;
    private String fieldNumber;

    @Enumerated(EnumType.STRING)
    private Enums.EtpCategory categoryOfETP;
}
