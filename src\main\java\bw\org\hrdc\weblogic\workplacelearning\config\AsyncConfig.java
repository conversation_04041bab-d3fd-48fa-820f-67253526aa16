package bw.org.hrdc.weblogic.workplacelearning.config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;


/**
 * <AUTHOR>
 * @CreatedOn 25/06/25 11:59
 * @UpdatedBy martinspectre
 * @UpdatedOn 25/06/25 11:59
 */

@Configuration
@EnableAsync
public class AsyncConfig {
    @Bean(name = "workflowExecutor")
    public Executor workflowExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);          // Minimum number of threads
        executor.setMaxPoolSize(10);          // Maximum number of threads
        executor.setQueueCapacity(100);       // Queue size before new threads are spawned
        executor.setThreadNamePrefix("WorkflowAsync-");
        executor.setWaitForTasksToCompleteOnShutdown(true); // Graceful shutdown
        executor.initialize();
        return executor;
    }
}
