package bw.org.hrdc.weblogic.workplacelearning.util;

/**
 * <AUTHOR>
 * @CreatedOn 25/02/25 21:45
 * @UpdatedBy martinspectre
 * @UpdatedOn 25/02/25 21:45
 */
public class Enums {
    public enum AuditEvent {
        INSERT, UPDATE, DELETE
    }
    public enum UserRoles {
        AGENT,
        AGENT_LEAD,
        OFFICER,
        OFFICER_LEAD,
        MANAGER
    }
    public enum LearningOutcome {
        TOPICS,
        OBJECTIVES,
        DELIVERY_STRATEGIES,
        ASSESSMENT_STRATEGIES
    }
    public enum EtpCategory {
        Conventional, Online, Hybrid
    }
    public enum DocumentType {
        BQA_ACCREDITATION_CERTIFICATE,
        CIPA_CERTIFICATE
    }
    public enum State {
        DRAFT, //Applicant did not submit
        PAYMENT, //Application at quotation or POP state
        SUBMITTED, //Applicant submitted, not yet assigned for assessment
        IN_PROCESSING, //Assigned to Agent for vetting
        IN_REVIEW, // Assigned to officer, under vetting
        IN_APPROVAL, //Assigned to manager for approval
    }

    public enum Status {
        INITIAL, //Not yet submitted, still in draft state
        PENDING, //Not in draft and has been submitted but not yet acted upon
        APPROVED, //Accepted by the manager and the application is closed
        REJECTED, //Did not meet requirements
        CHANGE_REQUEST, //Changes requested
        PRE_APPROVED, //Approved by the officer/officer lead under review state
        EXPIRED, //Still in draft, Time allowed passed before it was submitted
        CANCELLED, //Still under draft/ submitted but was not assigned Closed while still awaiting processing
        PENDING_PAYMENT, //Payment acknowledgement pending
    }


    public enum ComplaintStatus {
        OPEN, AWAITING_CLIENT_FEEDBACK, IN_PROGRESS, CLOSED
    }

    public enum ComplaintState {
        SUBMITTED, UNDER_REVIEW, ESCALATED, COMPLETED, REJECTED
    }

    public enum Department {
        FUND_ADMINISTRATION,
        WORKPLACE_LEARNING,
        NON_CREDIT_BEARING_SHORT_COURSE,
        GENERAL,
    }

    public enum CategoryComplaint {
        COMPLAINT,
        APPEAL,
    }

    public enum ChangeSeverity {
        MAJOR, MINOR
    }

    public enum QuoteStatus {
        PENDING, ACCEPTED, REJECTED
    }

    public enum ApplicationType{
        RECOGNITION,
        NOC,
        PRE_APPROVAL,
        WORK_SKILLS,
        APPEALS,
        COMPLAINTS
    }

    public enum NotificationType {
        IN_APP, // In-app notification
        EMAIL, // Email notification
        SMS // SMS notification
    }

    public enum FileEntityType{
        NCBSC_APPLICATION,
        NOC_APPLICATION,
        TRAINING_PLAN_APPLICATION,
        PRE_APPROVAL_APPLICATION,
        TRAINING_PLAN_NOC_APPLICATION,
    }

   }
