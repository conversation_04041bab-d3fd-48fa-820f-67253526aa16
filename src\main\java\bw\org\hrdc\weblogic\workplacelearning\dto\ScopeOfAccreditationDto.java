package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.UUID;

/**
 * DTO for managing Scope of Accreditation details.
 */
@Data
@Schema(
        name = "ScopeOfAccreditation",
        description = "Schema to hold Scope of Accreditation details"
)
public class ScopeOfAccreditationDto {

    @Schema(description = "Unique identifier for the scope of accreditation", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @NotNull(message = "Fields of learning accredited cannot be null")
    @Schema(description = "Fields of learning accredited", example = "Information Technology")
    private String fieldsOfLearningAccredited;
}