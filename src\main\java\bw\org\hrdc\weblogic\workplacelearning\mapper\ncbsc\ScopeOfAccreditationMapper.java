package bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.ScopeOfAccreditationDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ScopeOfAccreditation;

import java.util.Set;
import java.util.stream.Collectors;

public class ScopeOfAccreditationMapper {

    public static ScopeOfAccreditation toEntity(ScopeOfAccreditationDto dto, NCBSCApplication application) {
        if (dto == null) {
            return null;
        }

        ScopeOfAccreditation entity = new ScopeOfAccreditation();
        entity.setFieldsOfLearningAccredited(dto.getFieldsOfLearningAccredited());
        entity.setApplication(application);
        return entity;
    }

    public static ScopeOfAccreditationDto toDto(ScopeOfAccreditation entity) {
        if (entity == null) {
            return null;
        }

        ScopeOfAccreditationDto model = new ScopeOfAccreditationDto();

        model.setFieldsOfLearningAccredited(entity.getFieldsOfLearningAccredited());

        return model;
    }

    public static Set<ScopeOfAccreditation> toEntityList(Set<ScopeOfAccreditationDto> dto, NCBSCApplication application) {
        if (dto == null) {
            return null;
        }

        return dto.stream().map(scope -> ScopeOfAccreditationMapper.toEntity(scope, application)).collect(Collectors.toSet());
    }

    public static Set<ScopeOfAccreditationDto> toDtoList(Set<ScopeOfAccreditation> entity) {
        if (entity == null) {
            return null;
        }

        return entity.stream().map(ScopeOfAccreditationMapper::toDto).collect(Collectors.toSet());
    }
}
