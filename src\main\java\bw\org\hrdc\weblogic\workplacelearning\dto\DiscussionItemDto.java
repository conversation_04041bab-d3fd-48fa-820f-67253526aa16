package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.UUID;

/**
 * DTO for discussion items in ETP evaluation.
 */
@Data
@Schema(
        name = "DiscussionItem",
        description = "Schema to hold details of a discussion item in ETP evaluation"
)
public class DiscussionItemDto {

    @Schema(description = "Unique identifier for the discussion item", example = "19edd7ec-04f3-32ca-81ed-16da935d38a9")
    private UUID id;

    @Schema(description = "Label describing the discussion item", example = "Introductions")
    private String label;

    @Schema(description = "Facilitator from HRDC", example = "John Doe")
    private String facilitatorHRDC;

    @Schema(description = "Facilitator from ETP", example = "Jane Smith")
    private String facilitatorETP;

    @Schema(description = "Number of courses offered by the ETP", example = "10")
    private String noOfCoursesOffered;

    @Schema(description = "Number of courses duly recognized by the ETP", example = "8")
    private String noOfCoursesDulyRecognized;
}
