package bw.org.hrdc.weblogic.workplacelearning.controller;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.*;
import bw.org.hrdc.weblogic.workplacelearning.service.ncbsc.CourseService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse.getInternalServerError;

/**
 * <AUTHOR>
 * @CreatedOn 20/02/25 18:47
 * @UpdatedBy martinspectre
 * @UpdatedOn 20/02/25 18:47
 */
@RestController
@RequestMapping("/api/v1/ncbsc/recognition/courses")
public class CourseController {
    private static final Logger logger = LoggerFactory.getLogger(CourseController.class);
    @Autowired
    private CourseService courseService;

    @GetMapping("/{companyId}/all")
    public ResponseEntity<ApiResponse<?>> getAllCoursesByCompany(@PathVariable String companyId ) {
        try {

            List<ShortCourseInformationListDto> courses = courseService.getShortCoursesByOrganisation(companyId);

            if (courses.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", courses, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{courseReference}/available-topics")
    public ResponseEntity<ApiResponse<?>> getTopicsByCourseId(@PathVariable String courseReference) {
        try {
            List<AssessmentCriteriaExtendedDto> topics = courseService.getCourseTopics(courseReference);
            if(topics != null && !topics.isEmpty()){
                return ResponseEntity.ok(new ApiResponse<>(true, "Available topics", topics, null));
            }else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "No topics found", null, null));
            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }
}
