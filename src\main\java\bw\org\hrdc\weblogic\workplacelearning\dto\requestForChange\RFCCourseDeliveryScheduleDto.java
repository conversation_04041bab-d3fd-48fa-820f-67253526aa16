package bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * DTO for managing Course Delivery Schedule.
 */
@Data
public class RFCCourseDeliveryScheduleDto {

    private UUID uuid;

    @NotNull(message = "Date cannot be null")
    @Schema(description = "The date of the scheduled course delivery", example = "2024-01-15")
    private Date date;

    @NotNull(message = "Topic cannot be null")
    @Schema(description = "The topic for the scheduled course delivery", example = "Introduction to Programming")
    private String topic;

    @NotNull(message = "Hours cannot be null")
    @Schema(description = "The hours for the scheduled delivery", example = "3")
    private String hours;
}
