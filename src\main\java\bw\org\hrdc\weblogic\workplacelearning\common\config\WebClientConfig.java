package bw.org.hrdc.weblogic.workplacelearning.common.config;

import jakarta.inject.Inject;
import org.hibernate.service.spi.InjectService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * <AUTHOR>
 * @CreatedOn 27/03/25 22:59
 * @UpdatedBy martinspectre
 * @UpdatedOn 27/03/25 22:59
 */
@Configuration
public class WebClientConfig {
    @Bean
    public WebClient webClient(WebClient.Builder builder) {
        return builder.build();
    }
}

