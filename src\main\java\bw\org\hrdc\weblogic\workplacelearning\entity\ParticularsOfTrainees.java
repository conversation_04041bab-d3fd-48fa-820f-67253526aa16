package bw.org.hrdc.weblogic.workplacelearning.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "particulars_of_trainees")
@Getter
@Setter
@ToString
public class ParticularsOfTrainees {

    @Id
    @GeneratedValue
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "pre_approval_application_id", nullable = false)
    private PreApprovalApplication preApprovalApplication;

    @Column(name = "surname")
    private String surname;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "passport_or_omang_no")
    private String passportOrOmangNo;

    @Column(name = "gender")
    private String gender;
}
