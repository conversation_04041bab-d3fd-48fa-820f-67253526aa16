package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * DTO for compliance criteria in ETP evaluation.
 */
@Data
@Schema(
        name = "ComplianceCriteria",
        description = "Schema to hold details of compliance criteria in ETP evaluation"
)
public class ComplianceCriteriaDto {

    @Schema(description = "Part of the compliance criteria", example = "Part A")
    private String part;

    @Schema(description = "Specific compliance criteria", example = "Accreditation Status & Course Information")
    private String criteria;

    @Schema(description = "List of fields and their compliance status")
    private List<ComplianceFieldDto> fields;
}
