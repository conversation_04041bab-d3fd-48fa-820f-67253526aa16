package bw.org.hrdc.weblogic.workplacelearning.util;

import java.util.UUID;

/**
 * <AUTHOR>
 * @CreatedOn 15/04/25 12:06
 * @UpdatedBy martinspectre
 * @UpdatedOn 15/04/25 12:06
 */
public class Common {
    public static long defaultId(Long id) {
        return id != null ? id : System.nanoTime();
    }

    public static String defaultUuid(String uuid) {
        return uuid != null ? uuid : UUID.randomUUID().toString();
    }
}
