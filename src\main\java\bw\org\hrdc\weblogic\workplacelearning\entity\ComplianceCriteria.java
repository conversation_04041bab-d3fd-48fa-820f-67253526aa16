package bw.org.hrdc.weblogic.workplacelearning.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "compliance_criteria")
@Getter
@Setter
@ToString
public class ComplianceCriteria {

    @Id
    @GeneratedValue
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "evaluation_id", nullable = false)
    private ETPEvaluation evaluation;

    @Column(name = "part", nullable = false)
    private String part;

    @Column(name = "criteria", nullable = false)
    private String criteria;

    @OneToMany(mappedBy = "criteria", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ComplianceField> fields;
}

