<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <!-- Add REJECTED state to Appeal_state_check constraint -->
    <changeSet author="developer" id="update-complaints-state-constraint-add-rejected-20250623">
        <comment>Update complaints_state_check constraint to include REJECTED state for appeals</comment>
        
        <!-- Drop existing constraint -->
        <sql>
            ALTER TABLE complaints DROP CONSTRAINT IF EXISTS complaints_state_check;
        </sql>
        
        <!-- Recreate constraint with REJECTED state included -->
        <sql>
            ALTER TABLE complaints ADD CONSTRAINT complaints_state_check 
            CHECK (state IN ('SUBMITTED', 'UNDER_REVIEW', 'ESCALATED', 'COMPLETED', 'REJECTED'));
        </sql>
        
        <!-- Rollback instructions -->
        <rollback>
            <!-- Drop the updated constraint -->
            ALTER TABLE complaints DROP CONSTRAINT IF EXISTS complaints_state_check;
            
            <!-- Recreate original constraint without REJECTED -->
            ALTER TABLE complaints ADD CONSTRAINT complaints_state_check 
            CHECK (state IN ('SUBMITTED', 'UNDER_REVIEW', 'ESCALATED', 'COMPLETED'));
        </rollback>
    </changeSet>

</databaseChangeLog>