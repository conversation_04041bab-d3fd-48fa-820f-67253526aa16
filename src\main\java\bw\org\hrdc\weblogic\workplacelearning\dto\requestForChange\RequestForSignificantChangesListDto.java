package bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
public class RequestForSignificantChangesListDto {
    private String referenceNumber;
    private UUID organisationId;
    private String companyName;
    private String courseTitle;
    private String assignedTo;
    private LocalDateTime dateSubmitted;
    private String applicationStatus;
    private String applicationState;
}
