package bw.org.hrdc.weblogic.workplacelearning.service.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplicationComments;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition.NCBSCApplicationCommentsRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class NCBSCApplicationCommentsService {

    @Autowired
    private NCBSCApplicationCommentsRepo commentsRepo;

    public List<NCBSCApplicationComments> getAuditLogsForApplication(String applicationId) {
        return commentsRepo.findByCommentsApplicationId(applicationId);
    }

    @Transactional
    public void createComments(NCBSCApplicationComments comments) {
        commentsRepo.save(comments);
    }
}
