package bw.org.hrdc.weblogic.workplacelearning.rfai.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "additional_information")
@Data
public class AdditionalInformation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String criteria;
    private String detailsOfAdditionalInformationRequired;
    private boolean provided;

    @ManyToOne
    @JoinColumn(name = "request_for_additional_information_id")
    private RequestForAdditionalInformation requestForAdditionalInformation;
}
