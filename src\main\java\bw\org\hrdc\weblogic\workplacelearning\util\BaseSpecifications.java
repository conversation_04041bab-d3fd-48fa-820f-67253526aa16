package bw.org.hrdc.weblogic.workplacelearning.util;

import org.springframework.data.jpa.domain.Specification;

/**
 * <AUTHOR>
 * @CreatedOn 20/02/25 21:33
 * @UpdatedBy martinspectre
 * @UpdatedOn 20/02/25 21:33
 */
public class BaseSpecifications {

    public static <T> Specification<T> isNotDeleted() {
        return (root, query, criteriaBuilder) -> criteriaBuilder.isFalse(root.get("isDeleted"));
    }

    public static <T> Specification<T> isComplaint() {
        return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("category"), Enums.CategoryComplaint.COMPLAINT);
    }

    public static <T> Specification<T> isAppeal() {
        return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("category"), Enums.CategoryComplaint.APPEAL);
    }
}
