package bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.UUID;

/**
 * DTO for managing Details of Significant Change.
 */
@Data
public class RFCDetailsOfSignificantChangeDto {

    @Schema(description = "Unique identifier for the significant change details", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID uuid;

    @NotNull(message = "Request ID cannot be null")
    @Schema(description = "Unique identifier for the associated request for significant changes", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID requestForSignificantChangesId;

    @NotNull(message = "Criteria cannot be null")
    @Schema(description = "Criteria for the significant change", example = "Course Title")
    private String criteria;

    @NotNull(message = "Details cannot be null")
    @Schema(description = "Detailed information about the change", example = "Change the course title from 'Basic Computing' to 'Introduction to Computing'")
    private String details;
}
