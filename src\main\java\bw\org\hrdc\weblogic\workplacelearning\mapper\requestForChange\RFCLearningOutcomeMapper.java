package bw.org.hrdc.weblogic.workplacelearning.mapper.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.RFCLearningOutcomeDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RFCLearningOutcome;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RequestForSignificantChanges;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
public class RFCLearningOutcomeMapper {

    public static RFCLearningOutcomeDto toDto(RFCLearningOutcome entity) {
        if (entity == null) {
            return null;
        }

        RFCLearningOutcomeDto dto = new RFCLearningOutcomeDto();
        dto.setUuid(UUID.fromString(entity.getUuid())); // Assuming entity has an id field
        dto.setTopic(entity.getTopic());
        dto.setObjective(entity.getObjective());
        dto.setDeliveryStrategy(entity.getDeliveryStrategy());
        dto.setAssessmentStrategy(entity.getAssessmentStrategy());
        dto.setOutcome(entity.getOutcome());
        return dto;
    }

    public static RFCLearningOutcome toEntity(RFCLearningOutcomeDto dto, RequestForSignificantChanges application) {
        if (dto == null) {
            return null;
        }

        RFCLearningOutcome entity = new RFCLearningOutcome();
        entity.setUuid(String.valueOf(dto.getUuid())); // Assuming LearningOutcome entity has an id field
        entity.setApplication(application); // Set parent reference
        entity.setTopic(dto.getTopic());
        entity.setObjective(dto.getObjective());
        entity.setDeliveryStrategy(dto.getDeliveryStrategy());
        entity.setAssessmentStrategy(dto.getAssessmentStrategy());
        entity.setOutcome(dto.getOutcome());

        return entity;
    }

    public static List<RFCLearningOutcomeDto> toDtoList(List<RFCLearningOutcome> entities) {
        return entities.stream()
                .map(RFCLearningOutcomeMapper::toDto)
                .collect(Collectors.toList());
    }
}
