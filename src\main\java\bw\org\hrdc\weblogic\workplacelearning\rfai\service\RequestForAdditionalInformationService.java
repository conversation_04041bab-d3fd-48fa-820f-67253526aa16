package bw.org.hrdc.weblogic.workplacelearning.rfai.service;


import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import bw.org.hrdc.weblogic.workplacelearning.rfai.dto.RequestForAdditionalInformationDTO;
import bw.org.hrdc.weblogic.workplacelearning.rfai.dto.RequestForAdditionalInformationRequest;
import bw.org.hrdc.weblogic.workplacelearning.rfai.dto.RequestForAdditionalInformationResponse;
import bw.org.hrdc.weblogic.workplacelearning.rfai.entity.RequestForAdditionalInformation;

import java.util.List;

public interface RequestForAdditionalInformationService {
    RequestForAdditionalInformationResponse createAdditionalInformation(RequestForAdditionalInformationRequest request);
    List<RequestForAdditionalInformationResponse> getAllAdditionalInformation();
    RequestForAdditionalInformationResponse getAdditionalInformationById(Long id);
    RequestForAdditionalInformationResponse updateAdditionalInformation(Long id, RequestForAdditionalInformationRequest request);
//    List<RequestForAdditionalInformationResponse> getApplicationsByStatus(ApplicationStatus status);
    void deleteApplication(Long id);
}