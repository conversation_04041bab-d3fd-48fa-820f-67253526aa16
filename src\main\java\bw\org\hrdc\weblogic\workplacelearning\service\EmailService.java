package bw.org.hrdc.weblogic.workplacelearning.service;

import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Qualifier;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class EmailService {

    private final KafkaTemplate<String, String> kafkaTemplate;

    private final ObjectMapper objectMapper;

    public EmailService(@Qualifier("stringKafkaTemplate") KafkaTemplate<String, String> kafkaTemplate, 
                           ObjectMapper objectMapper) {
            this.kafkaTemplate = kafkaTemplate;
            this.objectMapper = objectMapper;
    }

    private static final String EMAIL_TOPIC = "sms-email-notification";

    public void sendEmail(String userEmail, String userName, String subject, String message, 
                                String applicationType) {
        if (userEmail == null || userEmail.trim().isEmpty()) {
            log.warn("Cannot send email notification: email address is empty");
            return;
        }

        try {
            // Create email request in the exact format expected by existing EmailService
            Map<String, Object> emailRequest = new HashMap<>();
            
            // Standard EmailRequest fields (matching existing structure)
            emailRequest.put("contactAddress", userEmail);
            emailRequest.put("name", userName);
            emailRequest.put("subject", subject);
            emailRequest.put("body", message);
            emailRequest.put("communicationType", "EMAIL");
            emailRequest.put("applicationType", applicationType);
            // emailRequest.put("applicationStatus", approvedStatus);
            
            // Optional fields that won't break existing functionality
            emailRequest.put("userId", userEmail);
            emailRequest.put("toMail", userEmail);

            // Convert to JSON
            String emailJson = objectMapper.writeValueAsString(emailRequest);

            // Send to existing email topic
            kafkaTemplate.send(EMAIL_TOPIC, emailJson)
                .whenComplete((result, ex) -> {
                    if (ex == null) {
                        try {
                            log.info("Workflow email sent to [{}] - topic: {}, offset: {}, partition: {}",
                                    userEmail,
                                    result.getRecordMetadata().topic(),
                                    result.getRecordMetadata().offset(),
                                    result.getRecordMetadata().partition());
                        } catch (Exception metaEx) {
                            log.error("Error reading Kafka metadata for email to {}: {}", 
                                    userEmail, metaEx.getMessage(), metaEx);
                        }
                    } else {
                        log.error("Failed to send workflow email to [{}]: {}", userEmail, ex.getMessage(), ex);
                    }
                });

        } catch (Exception e) {
            log.error("Exception while sending workflow email to [{}]: {}", userEmail, e.getMessage(), e);
        }
    }

}
