package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.UUID;

/**
 * DTO for managing Assessment and Certification details.
 */
@Data
@Schema(
        name = "AssessmentAndCertification",
        description = "Schema to hold Assessment and Certification details"
)
public class AssessmentAndCertificationDto {

    @Schema(description = "Unique identifier for the assessment and certification", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @NotNull(message = "Certification requirements cannot be null")
    @Schema(description = "Details of certification requirements", example = "Complete all modules and pass the final exam")
    private String certificationRequirements;

    @NotNull(message = "Assessment strategy cannot be null")
    @Schema(description = "Details of the assessment strategy", example = "Continuous assessment and a final exam")
    private String assessmentStrategy;

    @NotNull(message = "Non-Credit Bearing Course ID cannot be null")
    @Schema(description = "The ID of the associated Non-Credit Bearing Course", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID nonCreditBearingCourseId;
}
