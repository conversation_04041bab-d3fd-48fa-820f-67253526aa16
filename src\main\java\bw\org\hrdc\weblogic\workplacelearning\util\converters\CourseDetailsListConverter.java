package bw.org.hrdc.weblogic.workplacelearning.util.converters;


import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.CourseDetailsDto;
import bw.org.hrdc.weblogic.workplacelearning.service.trainingPlan.TrainingPlanCRService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.databind.SerializationFeature;

@Converter
public class CourseDetailsListConverter implements AttributeConverter<List<CourseDetailsDto>, String> {

    private final ObjectMapper objectMapper;
    private static final Logger log = LoggerFactory.getLogger(CourseDetailsListConverter.class);

    public CourseDetailsListConverter() {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule()); // 👈 Important!
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS); // Optional: write ISO-8601 instead of timestamps
    }

    @Override
    public String convertToDatabaseColumn(List<CourseDetailsDto> attribute) {
        try {
            //log.info("Converting courseDetails: {}", attribute);
            return objectMapper.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error serializing courseDetails", e);
        }
    }

    @Override
    public List<CourseDetailsDto> convertToEntityAttribute(String dbData) {
        try {
            //log.info("Converting dbData to courseDetails: {}", dbData);
            JavaType type = objectMapper.getTypeFactory().constructCollectionType(List.class, CourseDetailsDto.class);
            return objectMapper.readValue(dbData, type);
        } catch (IOException e) {
            throw new RuntimeException("Error deserializing courseDetails", e);
        }
    }
}
