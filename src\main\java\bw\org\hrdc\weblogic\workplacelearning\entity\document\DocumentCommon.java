package bw.org.hrdc.weblogic.workplacelearning.entity.document;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Auditable;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @CreatedOn 23/06/25 10:00
 * @UpdatedBy martinspectre
 * @UpdatedOn 23/06/25 11:40
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "trn_documents")
@Data
@NoArgsConstructor @AllArgsConstructor
public class DocumentCommon extends Auditable implements Serializable {
    private String key;
    private String docName;
    private String docExt;
    private String identifier;
    private String fileType;
    private Integer docSize;
    private String fileUrl;
}
