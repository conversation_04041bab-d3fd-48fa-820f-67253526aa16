package bw.org.hrdc.weblogic.workplacelearning.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import java.util.List;
import java.util.UUID;

@Data
public class BatchStatusUpdateResult {
    private int totalProcessed;
    private int successCount;
    private int failureCount;
    private List<ApplicationUpdateResult> results;
    
    @Data
    @AllArgsConstructor
    public static class ApplicationUpdateResult {
        private UUID applicationId;
        private boolean success;
        private String message;
    }
}