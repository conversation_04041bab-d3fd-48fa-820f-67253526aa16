package bw.org.hrdc.weblogic.workplacelearning.service.impl;

import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplicationComments;
import bw.org.hrdc.weblogic.workplacelearning.repository.PreApprovalApplicationCommentsRepository;
import bw.org.hrdc.weblogic.workplacelearning.service.PreApprovalApplicationCommentsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class PreApprovalApplicationCommentsServiceImpl implements PreApprovalApplicationCommentsService {
    
    private final PreApprovalApplicationCommentsRepository commentsRepository;

    @Override
    public void createComments(PreApprovalApplicationComments comments) {
        commentsRepository.save(comments);
    }

    @Override
    public List<PreApprovalApplicationComments> getAuditLogsForApplication(UUID applicationId) {
        return commentsRepository.findByApplicationId(applicationId);
    }
}
