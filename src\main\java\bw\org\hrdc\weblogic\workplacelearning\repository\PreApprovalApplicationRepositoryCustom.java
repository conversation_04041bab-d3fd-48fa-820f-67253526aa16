package bw.org.hrdc.weblogic.workplacelearning.repository;

import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplication;
import org.springframework.data.jpa.domain.Specification;

import java.util.Map;

/**
 * Custom repository interface for PreApprovalApplication
 */
public interface PreApprovalApplicationRepositoryCustom {
    /**
     * Get all application status counts in a single query
     * @param baseSpec The base specification to filter applications
     * @return Map of status keys and their counts
     */
    Map<String, Long> getApplicationStatusCounts(Specification<PreApprovalApplication> baseSpec);
}
