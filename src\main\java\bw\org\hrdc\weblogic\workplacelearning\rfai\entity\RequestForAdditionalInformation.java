package bw.org.hrdc.weblogic.workplacelearning.rfai.entity;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "request_for_additional_information")
@Data
public class RequestForAdditionalInformation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(nullable = false)
    private String organisationId;
    @Column(nullable = false)
    private String submittedByUserId;

    @Column(nullable = false)
    private LocalDateTime dateSubmitted = LocalDateTime.now();

    @Enumerated(EnumType.STRING)
    private ApplicationStatus status;

    @Embedded
    private EtpDetails etpDetails;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "request_id")
    private List<AdditionalInformation> additionalInformationRequired = new ArrayList<>();
}
