package bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
* Created by <PERSON><PERSON><PERSON> on 26 November 2024
*/

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "course_delivery_schedule")
@Data
public class CourseDeliverySchedule extends Base implements Serializable {

     @Column(name = "date")
     private Date date;

     @Column(name = "topic")
     private String topic;

     @Column(name = "hours")
     private String hours;

     @ManyToOne
     @JoinColumn(name = "application_id", nullable = false)
     @ToString.Exclude
     @JsonIgnore
     private NCBSCApplication application;
}
