package bw.org.hrdc.weblogic.workplacelearning.repository.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RequestForSignificantChanges;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for RequestForSignificantChanges entity.
 */
@Repository
public interface RequestForSignificantChangesRepository extends JpaRepository<RequestForSignificantChanges, UUID>, JpaSpecificationExecutor<RequestForSignificantChanges> {

    /**
     * Find requests by status.
     *
     * @param applicationStatus the status of the requests (e.g., Pending, Approved, Rejected).
     * @return a list of requests matching the given status.
     */
    List<RequestForSignificantChanges> findByApplicationStatus(String applicationStatus);

    /**
     * Find requests by the person who requested them.
     *
     * @param requestedBy the username or identifier of the requester.
     * @return a list of requests created by the given person.
     */
    List<RequestForSignificantChanges> findByRequestedBy(String requestedBy);

    /**
     * Find requests by justification content containing a specific keyword.
     *
     * @param justificationKeyword the keyword to search within the justification field.
     * @return a list of requests where the justification contains the keyword.
     */
    List<RequestForSignificantChanges> findByJustificationContaining(String justificationKeyword);

    /**
     * Find requests that involve specific types of changes (e.g., title, course duration).
     *
     * @param isChangeOfTitle         whether the request involves a change of title.
     * @param isChangeOfCourseDuration whether the request involves a change of course duration.
     * @return a list of matching requests.
     */
    List<RequestForSignificantChanges> findByIsChangeOfTitleOrIsChangeOfCourseDuration(
            boolean isChangeOfTitle,
            boolean isChangeOfCourseDuration
    );

    /**
     * Find requests assigned to an agent
     *
     * @param assignedTo
     * @param pageable
     * @return a list of requests assigned to the agent
     */
    Page<RequestForSignificantChanges> findByAssignedTo(UUID assignedTo, Pageable pageable);

    /**
     * Find an application by referenceNumber
     *
     * @param referenceNumber
     * @return a single request
     */
    Optional<RequestForSignificantChanges> findByReferenceNumber(String referenceNumber);

}
