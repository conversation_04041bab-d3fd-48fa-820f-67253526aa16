package bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "course_content_delivery",
        uniqueConstraints = {
            @UniqueConstraint(columnNames = {"uuid", "application_id"})
        }
)
@Getter @Setter
public class CourseContentDelivery extends Base implements Serializable {

    @Column(name = "exit_level_outcomes", nullable = false)
    private String exitLevelOutcomes;

    @Column(name = "learning_outcomes_summary")
    private String learningOutcomesSummary;

    @Column(name = "short_course_delivery_mode")
    private String shortCourseDeliveryMode;

    @Column(name = "short_course_delivery_type")
    private String shortCourseDeliveryType;

    @Column(name = "location")
    private String location;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "application_id", nullable = false)
    @JsonIgnore
    private NCBSCApplication application;

    @OneToMany(mappedBy = "courseContentDelivery", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private Set<LearningOutcome> learningOutcomes;
}
