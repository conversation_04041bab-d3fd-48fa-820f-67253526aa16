package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.UUID;

/**
 * DTO for managing Estimated Training Costs.
 */
@Data
@Schema(
        name = "EstimatedTrainingCosts",
        description = "Schema to hold estimated training costs"
)
public class EstimatedTrainingCostsDto {

    @Schema(description = "Unique identifier for the training cost", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @Schema(description = "Description of the cost item", example = "Tuition Fees")
    private String itemDescription;

    @Schema(description = "Cost amount in Pula", example = "10000.0")
    private Double amount;
}
