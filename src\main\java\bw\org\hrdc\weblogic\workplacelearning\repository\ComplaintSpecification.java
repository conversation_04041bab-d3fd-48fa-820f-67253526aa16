package bw.org.hrdc.weblogic.workplacelearning.repository;

import bw.org.hrdc.weblogic.workplacelearning.entity.Complaint;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDate;
import java.util.UUID;

/**
 * Specification for Complaint entity.
 */
public class ComplaintSpecification {

    /**
     * Creates a specification for filtering Complaints by various criteria.
     *
     * @param status          the status of the complaint (e.g., "Open", "Closed").
     * @param appellantName   the name of the appellant (case insensitive).
     * @param applicationId   the ID of the application related to the complaint.
     * @param organisationId  the ID of the organisation associated with the complaint.
     * @param userId          the ID of the user who filed the complaint.
     * @param startDate       the start date of the submission range.
     * @param endDate         the end date of the submission range.
     * @param reasonKeyword   a keyword to search for in the reason (case insensitive).
     * @return a specification for filtering Complaints.
     */
    public static Specification<Complaint> searchByCriteria(
            String status,
            String appellantName,
            UUID applicationId,
            UUID organisationId,
            UUID userId,
            LocalDate startDate,
            LocalDate endDate,
            String reasonKeyword) {
        return (root, query, cb) -> {
            Predicate p = cb.conjunction();

            if (status != null) {
                p = cb.and(p, cb.equal(root.get("status"), status));
            }
            if (appellantName != null) {
                p = cb.and(p, cb.like(cb.lower(root.get("appellantName")), "%" + appellantName.toLowerCase() + "%"));
            }
            if (applicationId != null) {
                p = cb.and(p, cb.equal(root.get("applicationId"), applicationId));
            }
            if (organisationId != null) {
                p = cb.and(p, cb.equal(root.get("organisationId"), organisationId));
            }
            if (userId != null) {
                p = cb.and(p, cb.equal(root.get("userId"), userId));
            }
            if (startDate != null && endDate != null) {
                p = cb.and(p, cb.between(root.get("dateSubmitted"), startDate, endDate));
            }
            if (reasonKeyword != null) {
                p = cb.and(p, cb.like(cb.lower(root.get("reason")), "%" + reasonKeyword.toLowerCase() + "%"));
            }

            return p;
        };
    }
}