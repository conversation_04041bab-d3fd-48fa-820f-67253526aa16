package bw.org.hrdc.weblogic.workplacelearning.dto;

import bw.org.hrdc.weblogic.workplacelearning.dto.document.FileDocumentDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

/**
 * DTO for PreApprovalApplication response.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(
        name = "PreApprovalApplicationResponse",
        description = "Schema to hold Pre-Approval Application response details"
)
public class PreApprovalApplicationResponseDto {

    @Schema(description = "Status code of the response", example = "201")
    private String statusCode;

    @Schema(description = "Status message of the response", example = "Pre-Approval Application created successfully.")
    private String statusMsg;

    @Schema(description = "ID of the created/updated application", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID applicationId;

    @Schema(description = "State of the application", example = "DRAFT")
    private String state;

    @Schema(description = "Status of the application", example = "INITIAL")
    private String status;

    @Schema(description = "Application number", example = "APP-20240408-ABC123")
    private String applicationNumber;

    @Schema(description = "Reference number", example = "REF-20240408-XYZ789")
    private String referenceNumber;

    @Schema(description = "VAT number for the application", example = "VAT-20240408-ABC123")
    private String vatNumber;

    @Schema(description = "Process instance ID for the application", example = "pi-1234567890")
    private String processInstanceId;

    private List<FileDocumentDto> attachments;

} 