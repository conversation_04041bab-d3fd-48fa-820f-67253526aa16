package bw.org.hrdc.weblogic.workplacelearning.mapper.document;
import bw.org.hrdc.weblogic.workplacelearning.dto.document.FileDocumentDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.document.FileDocument;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
public class FileDocumentMapper {

    public FileDocument toEntity(FileDocumentDto dto) {
        if (dto == null) {
            return null;
        }

        FileDocument entity = new FileDocument();
        //entity.setId(dto.getId() != null ? dto.getId() : UUID.randomUUID());
        entity.setKey(dto.getKey());
        entity.setDocName(dto.getDocName());
        entity.setDocExt(dto.getDocExt());
        entity.setIdentifier(dto.getIdentifier());
        entity.setFileType(dto.getFileType());
        entity.setDocSize(dto.getDocSize());
        entity.setFileUrl(dto.getFileUrl());
        entity.setInputIdentifier(dto.getInputIdentifier());
        entity.setEntityId(dto.getEntityId());

        return entity;
    }

    public FileDocumentDto toDto(FileDocument entity) {
        if (entity == null) {
            return null;
        }

        FileDocumentDto dto = new FileDocumentDto();
        //dto.setId(entity.getId());
        dto.setKey(entity.getKey());
        dto.setDocName(entity.getDocName());
        dto.setDocExt(entity.getDocExt());
        dto.setIdentifier(entity.getIdentifier());
        dto.setFileType(entity.getFileType());
        dto.setDocSize(entity.getDocSize());
        dto.setFileUrl(entity.getFileUrl());
        dto.setInputIdentifier(entity.getInputIdentifier());
        dto.setEntityId(entity.getEntityId());

        return dto;
    }

    public List<FileDocument> toEntityList(List<FileDocumentDto> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }

    public List<FileDocumentDto> toDtoList(List<FileDocument> entityList) {
        if (entityList == null) {
            return null;
        }
        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}