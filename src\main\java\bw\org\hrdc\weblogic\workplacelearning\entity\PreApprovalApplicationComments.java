package bw.org.hrdc.weblogic.workplacelearning.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "pre_approval_application_comments")
@Getter
@Setter
@ToString
public class PreApprovalApplicationComments {

    @Id
    @GeneratedValue
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "pre_approval_application_id", nullable = false)
    @JsonIgnoreProperties({"particularOfTrainings", "moduleDetails", "estimatedTrainingCosts", "comments"})
    private PreApprovalApplication application;

    @Column(nullable = false)
    private String action;  // e.g., "APPROVED", "REJECTED", "ASSIGNED"

    @Column(length = 500)
    private String comments;

    @Column(nullable = false)
    private String updatedBy;

    @Column(nullable = false)
    private LocalDateTime timestamp = LocalDateTime.now();

   @Column(name = "batch_id", columnDefinition = "uuid")
    private UUID batchId;

    } 