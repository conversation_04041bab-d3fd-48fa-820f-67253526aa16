package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(
    name = "PreApprovalApplicationComments",
    description = "Schema to hold Pre-Approval Application Comments details"
)
public class PreApprovalApplicationCommentsDto {
    
    @Schema(description = "Unique identifier for the comment", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;
    
    @Schema(description = "ID of the pre-approval application this comment belongs to", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID applicationId;
    
    @Schema(description = "Action taken (e.g., APPROVED, REJECTED, ASSIGNED)", example = "APPROVED")
    private String action;
    
    @Schema(description = "Comments about the action", example = "Application meets all requirements")
    private String comments;
    
    @Schema(description = "User who made the comment", example = "<EMAIL>")
    private String updatedBy;
    
    @Schema(description = "Timestamp when the comment was made")
    private LocalDateTime timestamp;
} 