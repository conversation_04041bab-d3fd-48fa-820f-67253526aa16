package bw.org.hrdc.weblogic.workplacelearning;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableJpaAuditing(auditorAwareRef = "auditAwareImpl")
@EnableFeignClients
@EnableScheduling
@OpenAPIDefinition()
@EnableDiscoveryClient
@EnableAsync
public class WorkplaceLearningApplication {

    private static final Logger logger = LoggerFactory.getLogger(WorkplaceLearningApplication.class);

    public static void main(String[] args) {

        logger.info("Workplace Learning Service Management initiated");
        SpringApplication.run(WorkplaceLearningApplication.class, args);
    }

}
