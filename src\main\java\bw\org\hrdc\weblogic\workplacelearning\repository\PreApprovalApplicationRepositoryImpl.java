package bw.org.hrdc.weblogic.workplacelearning.repository;

import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplication;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 * Implementation of custom repository methods for PreApprovalApplication
 */
@Repository
public class PreApprovalApplicationRepositoryImpl implements PreApprovalApplicationRepositoryCustom {

    private static final Logger logger = LoggerFactory.getLogger(PreApprovalApplicationRepositoryImpl.class);

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Map<String, Long> getApplicationStatusCounts(Specification<PreApprovalApplication> baseSpec) {
        Map<String, Long> statusCounts = new HashMap<>();
        
        try {
            // Convert the JPA Specification to a CriteriaQuery
            CriteriaBuilder cb = entityManager.getCriteriaBuilder();
            CriteriaQuery<Tuple> query = cb.createTupleQuery();
            Root<PreApprovalApplication> root = query.from(PreApprovalApplication.class);
            
            // Apply the base specification
            Predicate basePredicate = baseSpec.toPredicate(root, query, cb);
            query.where(basePredicate);
            
            // Select the counts we need
            query.multiselect(
                cb.count(root).alias("totalApplications"),
                cb.sum(cb.<Long>selectCase()
                    .when(cb.and(
                        cb.equal(root.get("status"), "PENDING"),
                        cb.equal(root.get("state"), "SUBMITTED")
                    ), 1L).otherwise(0L)).alias("pendingVetting"),
                cb.sum(cb.<Long>selectCase()
                    .when(cb.and(
                        cb.equal(root.get("status"), "PENDING"),
                        cb.equal(root.get("state"), "IN_PROCESSING")
                    ), 1L).otherwise(0L)).alias("vettingOngoing"),
                cb.sum(cb.<Long>selectCase()
                    .when(cb.equal(root.get("status"), "CHANGE_REQUEST"), 1L)
                    .otherwise(0L)).alias("awaitingChanges"),
                cb.sum(cb.<Long>selectCase()
                    .when(cb.equal(root.get("status"), "REJECTED"), 1L)
                    .otherwise(0L)).alias("rejected"),
                cb.sum(cb.<Long>selectCase()
                    .when(cb.equal(root.get("status"), "APPROVED"), 1L)
                    .otherwise(0L)).alias("approved"),
                cb.sum(cb.<Long>selectCase()
                    .when(cb.and(
                        cb.equal(root.get("status"), "PENDING"),
                        cb.equal(root.get("state"), "IN_REVIEW")
                    ), 1L).otherwise(0L)).alias("underReview"),
                cb.sum(cb.<Long>selectCase()
                    .when(cb.or(
                        cb.and(
                            cb.equal(root.get("status"), "PENDING"),
                            cb.equal(root.get("state"), "IN_APPROVAL")
                        ),
                        cb.equal(root.get("status"), "PRE_APPROVED")
                    ), 1L).otherwise(0L)).alias("pendingApproval")
            );
            
            // Execute the query
            Tuple result = entityManager.createQuery(query).getSingleResult();
            
            // Map the results
            statusCounts.put("totalApplications", convertToLong(result.get("totalApplications")));
            statusCounts.put("pendingVetting", convertToLong(result.get("pendingVetting")));
            statusCounts.put("vettingOngoing", convertToLong(result.get("vettingOngoing")));
            statusCounts.put("awaitingChanges", convertToLong(result.get("awaitingChanges")));
            statusCounts.put("rejected", convertToLong(result.get("rejected")));
            statusCounts.put("approved", convertToLong(result.get("approved")));
            statusCounts.put("underReview", convertToLong(result.get("underReview")));
            statusCounts.put("pendingApproval", convertToLong(result.get("pendingApproval")));
            
            logger.info("Status counts with base specification: {}", statusCounts);
        } catch (Exception e) {
            logger.error("Error executing status count query with specification: {}", e.getMessage(), e);
            // Set default values in case of error
            statusCounts.put("totalApplications", 0L);
            statusCounts.put("pendingVetting", 0L);
            statusCounts.put("vettingOngoing", 0L);
            statusCounts.put("awaitingChanges", 0L);
            statusCounts.put("rejected", 0L);
            statusCounts.put("approved", 0L);
            statusCounts.put("underReview", 0L);
            statusCounts.put("pendingApproval", 0L);
        }
        
        return statusCounts;
    }
    
    private Long convertToLong(Object value) {
        if (value == null) {
            return 0L;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            logger.warn("Failed to convert {} to Long", value);
            return 0L;
        }
    }
}
