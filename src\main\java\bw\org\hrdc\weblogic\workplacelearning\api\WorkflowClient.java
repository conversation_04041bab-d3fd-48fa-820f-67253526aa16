package bw.org.hrdc.weblogic.workplacelearning.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import bw.org.hrdc.weblogic.workplacelearning.common.config.FeignConfig;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import java.util.Map;

@FeignClient(name = "WORKFLOW", fallback = WorkflowClientFallback.class, configuration = FeignConfig.class)
public interface WorkflowClient {

    @GetMapping("/api/v1/workflow/start-process/{applicationType}/{applicationNumber}")
    Map<String, Object> getNCBSCApplication(@PathVariable String applicationType, @PathVariable String applicationNumber);

    @PostMapping("/api/v1/workflow/resume-process/{processInstanceId}/{signalType}")
    Map<String, Object> resumeProcess(@PathVariable String processInstanceId, @PathVariable String signalType, Map<String, Object> workflowPayload);

    @PostMapping("/api/v1/complaintWorkflow/startprocess/{complaintId}")
    Map<String,Object>  getComplaintsWorkflow(@PathVariable String complaintId);

    @PostMapping("/api/v1/complaintWorkflow/resume-complaint-process/{processInstanceId}/{signalType}")
    Map<String, Object> resumeComplaintProcess(@PathVariable String processInstanceId, @PathVariable String signalType, Map<String, Object> WorkFlowPayload);

    @PostMapping("/api/v1/appealWorkflow/startprocess/{appealId}")
    Map<String, Object> getAppealWorkflow(@PathVariable String appealId);

    @PostMapping("/api/v1/appealWorkflow/resume-appeal-process/{processInstanceId}")
    Map<String, Object> resumeAppealProcess(@PathVariable String processInstanceId, Map<String, Object> workflowPayload);
}