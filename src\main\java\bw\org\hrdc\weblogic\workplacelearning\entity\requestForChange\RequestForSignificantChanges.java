package bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "request_for_significant_changes")
@EntityListeners(AuditingEntityListener.class)
@Getter
@Setter
@ToString
public class RequestForSignificantChanges {

    @JsonProperty(value="Id")
    @Column(nullable = false, updatable = false, unique = true)
    private Long id;

    @Id
    @Column(nullable = false, updatable = false, unique = true)
    @JsonProperty(value="Uuid")
    private UUID uuid = UUID.randomUUID();

    @Column(nullable = true)
    private UUID assignedTo;

    @Column(name = "ncbsc_application_id")
    private UUID ncbscApplicationId;

    @Column(name = "requested_by", nullable = false)
    private String requestedBy;

    @Column(name = "change_of_title")
    private boolean isChangeOfTitle;

    @Column(name = "change_of_course_duration")
    private boolean isChangeOfCourseDuration;

    @Column(name = "change_of_core_components")
    private boolean isChangeOfCoreComponents;

    @Column(name = "change_of_partnerships")
    private boolean isChangeOfPartnerships;

    @Column(name = "change_of_mode_of_delivery")
    private boolean isChangeOfModeOfDelivery;

    @Enumerated(EnumType.STRING)
    @Column(name = "application_status", nullable = false)
    private ApplicationStatus applicationStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "application_state", nullable = false)
    private ApplicationStatus applicationState;

    @Column(name = "reference_Number")
    private String referenceNumber;

    @Column(name = "justification", nullable = false)
    private String justification;

    @OneToMany(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnore
    @ToString.Exclude
    private List<RFCDetailsOfSignificantChange> detailsOfSignificantChanges;

    @OneToMany(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnore
    @ToString.Exclude
    private List<RFCLearningOutcome> learningOutcomes;

    @CreatedDate
    @Column(updatable = false)
    private Date createdAt;

    @Column(nullable = false, updatable = false)
    private UUID organisationId;

    @Column(nullable = false)
    private String assessmentPurpose;

    @Column(nullable = false)
    private String skillsNeedsAnalysis;

    @Column(nullable = false)
    private String shortCourseDeliveryMode;

    @Column(nullable = false)
    private String keyFacilitation;

    @Column(nullable = false)
    private String skillsAssessment;

    @Column(nullable = false)
    private String assessmentType;

    @Column(nullable = false)
    private String certification;

    @Column(nullable = false)
    private String resources;

    @Column(nullable = false)
    private String thirdPartyArrangements;

    @Column(nullable = false)
    private String shortCourseEndorsement;

    @Column(name = "scope_of_accreditation", nullable = false)
    private String scopeOfAccreditation;

    @OneToOne(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    @ToString.Exclude
    private RFCShortCourseInformation shortCourseInformation;

    @OneToOne(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    @ToString.Exclude
    private RFCCourseContentAndDelivery courseContentAndDelivery;

    @OneToMany(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnore
    @ToString.Exclude
    private List<RFCCourseDeliverySchedule> courseDeliverySchedule;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Date dateSubmitted = new Date();

    @LastModifiedDate
    @Column(insertable = false)
    private Date updatedAt;

    @Column(nullable = true)
    private String assignedAgent;

    @Column(nullable = true)
    private String assignedAgentLead;

    @Column(nullable = true)
    private String assignedOfficerLead;

    @Column(nullable = true)
    private String assignedOfficer;

    @Column(nullable = true)
    private String assignedManager;

    @PrePersist
    public void prePersist() {
        if (id == null) {
            id = System.currentTimeMillis();
        }
    }
}
