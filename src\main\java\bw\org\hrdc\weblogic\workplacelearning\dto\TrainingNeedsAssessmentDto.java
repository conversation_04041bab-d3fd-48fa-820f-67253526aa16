package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * DTO for managing Training Needs Assessment details.
 */
@Data
@Schema(
        name = "TrainingNeedsAssessment",
        description = "Schema to hold Training Needs Assessment details"
)
public class TrainingNeedsAssessmentDto {

    @NotNull(message = "Purpose cannot be null")
    @Schema(description = "The purpose of the training needs assessment", example = "Identify IT training gaps")
    private String purpose;

    @NotNull(message = "Skills needs analysis cannot be null")
    @Schema(description = "Skills needs analysis details", example = "Analysis of current vs required IT skills")
    private String skillsNeedsAnalysis;
}
