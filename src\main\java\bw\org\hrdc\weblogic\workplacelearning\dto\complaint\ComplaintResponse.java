package bw.org.hrdc.weblogic.workplacelearning.dto.complaint;

import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.AuditLog;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.Comment;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintDocument;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 28/03/25 11:23
 * @UpdatedBy martinspectre
 * @UpdatedOn 28/03/25 11:23
 */
@Data
public class ComplaintResponse {
    private String uuid;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;
    private String organisationId;
    private String organisation;
    private Enums.Department department;
    private String typeOfComplaint;
    private String description;
    private String reference;
    private String referenceNumber;
    private Enums.ComplaintStatus status;
    private Enums.ComplaintState state;
    private String assignedTo;
    private String assignedBy;
    private String processInstanceId;
    private List<ComplaintDocument> documents;
    private List<Comment> comments;
    private List<AuditLog> auditLogs;
    private ApplicationRejectionInfo originalApplication;
}
