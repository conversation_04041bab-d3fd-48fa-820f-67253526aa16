package bw.org.hrdc.weblogic.workplacelearning.mapper;

import bw.org.hrdc.weblogic.workplacelearning.dto.ETPEvaluationDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.CorrectiveActionProgressReportDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ETPEvaluation;
import bw.org.hrdc.weblogic.workplacelearning.entity.CorrectiveActionProgressReport;
import org.modelmapper.Converter;
import org.modelmapper.ModelMapper;
import org.modelmapper.spi.MappingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Mapper for converting between ETPEvaluation entity and ETPEvaluationDto.
 */
@Component
public class ETPEvaluationMapper {

    private final ModelMapper modelMapper;

    @Autowired
    public ETPEvaluationMapper(ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
        configureModelMapper();
    }

    /**
     * Configure ModelMapper with custom mappings.
     */
    private void configureModelMapper() {
        // Add custom mapping for UUID
        Converter<String, UUID> stringToUUIDConverter = new Converter<>() {
            @Override
            public UUID convert(MappingContext<String, UUID> context) {
                return context.getSource() == null ? null : UUID.fromString(context.getSource());
            }
        };

        Converter<UUID, String> uuidToStringConverter = new Converter<>() {
            @Override
            public String convert(MappingContext<UUID, String> context) {
                return context.getSource() == null ? null : context.getSource().toString();
            }
        };

        modelMapper.addConverter(stringToUUIDConverter, String.class, UUID.class);
        modelMapper.addConverter(uuidToStringConverter, UUID.class, String.class);

        // Customize mapping between DTO and entity for lists like correctiveActionProgressReports
        modelMapper.typeMap(ETPEvaluationDto.class, ETPEvaluation.class).addMappings(mapper -> {
            mapper.skip(ETPEvaluation::setCorrectiveActionProgressReports); // Custom handling
        });
    }

    /**
     * Convert Entity to DTO.
     *
     * @param entity the ETPEvaluation entity to convert
     * @return the corresponding ETPEvaluationDto
     */
    public ETPEvaluationDto toDto(ETPEvaluation entity) {
        return modelMapper.map(entity, ETPEvaluationDto.class);
    }

    /**
     * Convert DTO to Entity.
     *
     * @param dto the ETPEvaluationDto to convert
     * @return the corresponding ETPEvaluation entity
     */
    public ETPEvaluation toEntity(ETPEvaluationDto dto) {
        ETPEvaluation evaluation = modelMapper.map(dto, ETPEvaluation.class);

        // Handle merging of CorrectiveActionProgressReports
        if (dto.getCorrectiveActionProgressReports() != null) {
            List<CorrectiveActionProgressReport> updatedReports = new ArrayList<>();
            for (CorrectiveActionProgressReportDto reportDto : dto.getCorrectiveActionProgressReports()) {
                if (reportDto.getId() != null) {
                    // Find existing report and update it
                    Optional<CorrectiveActionProgressReport> existingReport = evaluation.getCorrectiveActionProgressReports()
                            .stream()
                            .filter(r -> r.getId().equals(reportDto.getId()))
                            .findFirst();

                    if (existingReport.isPresent()) {
                        modelMapper.map(reportDto, existingReport.get());
                        updatedReports.add(existingReport.get());
                    } else {
                        // Add new report
                        updatedReports.add(modelMapper.map(reportDto, CorrectiveActionProgressReport.class));
                    }
                } else {
                    // Add new report
                    updatedReports.add(modelMapper.map(reportDto, CorrectiveActionProgressReport.class));
                }
            }
            evaluation.setCorrectiveActionProgressReports(updatedReports);
        }

        return evaluation;
    }

    /**
     * Convert a Page of ETPEvaluation entities to a Page of ETPEvaluationDto.
     *
     * @param entityPage the Page of ETPEvaluation entities
     * @return a Page of ETPEvaluationDto
     */
    public Page<ETPEvaluationDto> toDtoPage(Page<ETPEvaluation> entityPage) {
        return entityPage.map(this::toDto);
    }

    /**
     * Convert a List of ETPEvaluation entities to a List of ETPEvaluationDto.
     *
     * @param entityList the List of ETPEvaluation entities
     * @return a List of ETPEvaluationDto
     */
    public List<ETPEvaluationDto> toDtoList(List<ETPEvaluation> entityList) {
        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert a List of ETPEvaluationDto to a List of ETPEvaluation entities.
     *
     * @param dtoList the List of ETPEvaluationDto
     * @return a List of ETPEvaluation entities
     */
    public List<ETPEvaluation> toEntityList(List<ETPEvaluationDto> dtoList) {
        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
