package bw.org.hrdc.weblogic.workplacelearning.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "etp_evaluation")
@Getter
@Setter
@ToString
public class ETPEvaluation {

    @Id
    @GeneratedValue
    private UUID id;

    @Column(name = "user_id", nullable = false)
    private UUID userId;

    @Column(name = "organisation_id", nullable = false)
    private UUID organisationId;

    @Temporal(TemporalType.DATE)
    @Column(name = "meeting_date")
    private Date meetingDate;

    @OneToMany(mappedBy = "etpEvaluation")
    private List<DiscussionItem> discussionItems;

    @OneToMany(mappedBy = "evaluation", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ComplianceCriteria> complianceCriteria;

    @Column(name = "assessment_summary", columnDefinition = "TEXT")
    private String assessmentSummary;

    @OneToMany(mappedBy = "evaluation", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ActionPlan> actionPlans;

    @OneToMany(mappedBy = "evaluation", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<CorrectiveActionProgressReport> correctiveActionProgressReports;
}
