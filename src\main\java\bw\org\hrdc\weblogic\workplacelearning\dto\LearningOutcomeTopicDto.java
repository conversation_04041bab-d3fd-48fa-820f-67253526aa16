package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * DTO for managing topics under a learning outcome.
 */
@Data
@Schema(
        name = "LearningOutcomeTopic",
        description = "Schema to hold details of a topic under a learning outcome"
)
public class LearningOutcomeTopicDto {

    @NotNull(message = "Topic unit cannot be null")
    @Schema(description = "Name of the topic unit", example = "Introduction to Python")
    private String topicUnit;

    @NotNull(message = "Objectives cannot be null")
    @Schema(description = "Learning objectives for the topic", example = "strings, dictionaries, tuples, etc.")
    private String objectives;

    @NotNull(message = "Delivery strategy cannot be null")
    @Schema(description = "Strategy for delivering the topic", example = "practical")
    private String deliveryStrategy;

    @NotNull(message = "Assessment strategy cannot be null")
    @Schema(description = "Strategy for assessing the topic", example = "build a simple program")
    private String assessmentStrategy;
}
