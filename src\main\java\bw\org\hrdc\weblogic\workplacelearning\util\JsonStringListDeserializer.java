package bw.org.hrdc.weblogic.workplacelearning.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class JsonStringListDeserializer extends JsonDeserializer<List<String>> {

    @Override
    public List<String> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        List<String> result = new ArrayList<>();
        
        if (p.getCurrentToken() == JsonToken.START_ARRAY) {
            // Handle regular JSON array
            while (p.nextToken() != JsonToken.END_ARRAY) {
                if (p.getCurrentToken() == JsonToken.VALUE_STRING) {
                    result.add(p.getValueAsString());
                }
            }
        } else if (p.getCurrentToken() == JsonToken.VALUE_STRING) {
            // Handle JSON array string
            String value = p.getValueAsString();
            if (value.startsWith("[") && value.endsWith("]")) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode node = mapper.readTree(value);
                    if (node.isArray()) {
                        for (JsonNode element : node) {
                            if (element.isTextual()) {
                                result.add(element.asText());
                            }
                        }
                    }
                } catch (Exception e) {
                    // If parsing fails, treat as single value
                    result.add(value);
                }
            } else {
                // Single value
                result.add(value);
            }
        }
        
        return result;
    }
} 