package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for PreApprovalApplication list view.
 */
@Data
@Schema(
        name = "PreApprovalApplicationList",
        description = "Schema to hold Pre-Approval Application list details"
)
public class PreApprovalApplicationListDto {
    
    @Schema(description = "Unique identifier for the pre-approval application", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;
    
    @Schema(description = "Unique identifier for the organization", example = "f7h8j90b-58cc-4372-a567-0e02b2c3d234")
    private UUID organisationId;
    
    @Schema(description = "Company name")
    private String companyName;
    
    @Schema(description = "Company physical address")
    private String physicalAddress;
    
    @Schema(description = "Company telephone number")
    private String telephoneNumber;
    
    @Schema(description = "Company fax number")
    private String faxNumber;
    
    @Schema(description = "Company email")
    private String email;
    
    @Schema(description = "Status of the application", example = "INITIAL")
    private String status;
    
    @Schema(description = "State of the application", example = "DRAFT")
    private String state;
    
    @Schema(description = "Application number", example = "APP-20240408-ABC123")
    private String applicationNumber;
    
    @Schema(description = "Reference number", example = "REF-20240408-XYZ789")
    private String referenceNumber;
    
    @Schema(description = "Reason for training", example = "To upskill employees for advanced tasks.")
    private String reasonForTraining;
    
    @Schema(description = "Title of the training course")
    private String courseTitle;
    
    @Schema(description = "Training provider")
    private String trainingProvider;
    
    @Schema(description = "VAT number")
    private String vatNumber;
    
    @Schema(description = "Assigned agent ID")
    private String assignedAgent;
    
    @Schema(description = "Assigned agent lead ID")
    private String assignedAgentLead;
    
    @Schema(description = "Assigned officer lead ID")
    private String assignedOfficerLead;
    
    @Schema(description = "Assigned officer ID")
    private String assignedOfficer;
    
    @Schema(description = "Assigned manager ID")
    private String assignedManager;
    
    @Schema(description = "Currently assigned user ID")
    private String assignedTo;
    
    @Schema(description = "Application creation date")
    private LocalDateTime createdDate;
    
   
}
