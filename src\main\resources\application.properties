spring.application.name=workplace-learning
# Default profile
spring.profiles.active=dev
#
# App Config
app.build.name=hrdc
app.build.version=@pom.version@
app.build.date=@build.timestamp@
#
# Security Configuration
spring.security.oauth2.resourceserver.jwt.issuer-uri=https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc/protocol/openid-connect/certs
# spring.security.oauth2.resourceserver.jwt.audiences=account,ehrdf

# Security Open Web Paths
# security.web.paths.open=/api/v1/oauth2/**
# security.web.paths.secured=/api/v1/pre-approval-applications/**,/**
#cors.mappings=/**::*
#
# Routes
security.route.authentication.controller=/api/auth
security.route.auth.controller=/api/auth
security.route.authentication.web=/v1/login
security.route.authentication.branch=/v1/branch
security.route.authentication.refresh=/v1/refresh
security.route.authentication.ping=/v1/hello
#
hrdc.api.workplace=/api/workplace
hrdc.api.workplace.fetchAll=/v1/list
hrdc.api.workplace.add=/v1/add
hrdc.api.workplace.fetchOne=/v1/list/{id}

