package bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "learning_outcomes")
@Getter @Setter
public class LearningOutcome extends Base implements Serializable {

    @Column(name = "outcome", nullable = false)
    private String outcome;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "course_content_id", nullable = false)
    @JsonIgnore
    @ToString.Exclude
    private CourseContentDelivery courseContentDelivery;

    @OneToMany(mappedBy = "learningOutcomes", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private Set<AssessmentCriteria> assessmentCriteria;
}
