package bw.org.hrdc.weblogic.workplacelearning.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.UUID;

@Entity
@Table(name = "discussion_item")
@Getter
@Setter
@ToString
public class DiscussionItem {

    @Id
    @GeneratedValue
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "etp_evaluation_id")
    private ETPEvaluation etpEvaluation;

    @Column(name = "label", nullable = false)
    private String label;

    @Column(name = "facilitator_hrdc")
    private String facilitatorHRDC;

    @Column(name = "facilitator_etp")
    private String facilitatorETP;

    @Column(name = "details_no_of_courses_offered")
    private String noOfCoursesOffered;

    @Column(name = "details_no_of_courses_duly_recognized")
    private String noOfCoursesDulyRecognized;
}

