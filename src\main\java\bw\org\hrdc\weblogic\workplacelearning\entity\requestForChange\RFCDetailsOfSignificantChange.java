package bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.UUID;

@Entity
@Table(name = "rfc_details_of_significant_change")
@Getter
@Setter
@ToString

public class RFCDetailsOfSignificantChange extends Base {

    @Column(name = "criteria", nullable = false)
    private String criteria;

    @Column(name = "details", nullable = false)
    private String details;

    @ManyToOne
    @JoinColumn(name = "application_id", nullable = false)
    @JsonIgnore
    @ToString.Exclude
    private RequestForSignificantChanges application;
}
