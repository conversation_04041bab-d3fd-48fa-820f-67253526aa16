package bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlanChangeRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.UUID;

/**
 * Repository for WorkPlaceTrainingPlanChangeRequest entity.
 */
@Repository
public interface TrainingPlanCRRepository
        extends JpaRepository<WorkPlaceTrainingPlanChangeRequest, UUID>, JpaSpecificationExecutor<WorkPlaceTrainingPlanChangeRequest>{

    @Modifying
    @Transactional
    @Query("UPDATE WorkPlaceTrainingPlanChangeRequest t SET " +
            "t.updatedAt = CURRENT_TIMESTAMP, " +
            "t.assignedAgent = CASE WHEN :role = 'AGENT' THEN :userId ELSE t.assignedAgent END, " +
            "t.assignedAgentLead = CASE WHEN :role = 'AGENT_LEAD' THEN :userId ELSE t.assignedAgentLead END, " +
            "t.assignedOfficer = CASE WHEN :role = 'OFFICER' THEN :userId ELSE t.assignedOfficer END, " +
            "t.assignedOfficerLead = CASE WHEN :role = 'OFFICER_LEAD' THEN :userId ELSE t.assignedOfficerLead END, " +
            "t.assignedManager = CASE WHEN :role = 'MANAGER' THEN :userId ELSE t.assignedManager END, " +
            "t.applicationState = " +
            "   CASE " +
            "       WHEN :role = 'AGENT' THEN 'IN_PROCESSING' " +
            "       WHEN :role = 'AGENT_LEAD' THEN 'SUBMITTED' " +
            "       WHEN :role = 'OFFICER' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'OFFICER_LEAD' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'MANAGER' THEN 'IN_APPROVAL' " +
            "       ELSE t.applicationState " +
            "   END " +
            "WHERE t.uuid = :id")
    int updateTrainingPlanAssignedUser(
            @Param("id") String id,
            @Param("role") String role,
            @Param("userId") String userId);

    @Modifying
    @Transactional
    @Query("UPDATE WorkPlaceTrainingPlanChangeRequest t " +
            "SET t.assignedOfficerLead = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN :newAssignee " +
            "    ELSE t.assignedOfficerLead " +
            "END, " +
            "t.applicationState = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN 'IN_REVIEW' " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN 'IN_APPROVAL' " +
            "    ELSE t.applicationState " +
            "END, " +
            "t.applicationStatus = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN 'PENDING' " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN 'PENDING' " +
            "    ELSE :action " +
            "END, " +
            "t.assignedManager = CASE " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN :newAssignee " +
            "    ELSE t.assignedManager " +
            "END, " +
            "t.assignedAgent = CASE " +
            "    WHEN :role = 'OFFICER' AND :action = 'CHANGE_REQUEST' THEN :newAssignee " +
            "    ELSE t.assignedAgent " +
            "END, " +
            "t.updatedAt = CURRENT_TIMESTAMP " +
            "WHERE t.uuid = :trainingPlanId")
    int changeTrainingPlanStatus(
            @Param("trainingPlanId") String trainingPlanId,
            @Param("role") String role,
            @Param("action") String action,
            @Param("newAssignee") String newAssignee);

    @Query("SELECT t FROM WorkPlaceTrainingPlanChangeRequest t LEFT JOIN FETCH t.trainingPlan WHERE t.uuid = :id")
    WorkPlaceTrainingPlanChangeRequest findByStringId(@Param("id") String id);

//    Map<String, Long> getTrainingPlanStatusCounts(Specification<WorkPlaceTrainingPlanChangeRequest> baseSpec);
}
