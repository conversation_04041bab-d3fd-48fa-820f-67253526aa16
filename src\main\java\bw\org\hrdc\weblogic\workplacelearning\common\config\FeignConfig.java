package bw.org.hrdc.weblogic.workplacelearning.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Request;
import feign.Response;
import feign.codec.Decoder;
import feign.codec.ErrorDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class FeignConfig {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Bean
    public Request.Options requestOptions() {
        // Set connection timeout to 30 seconds and read timeout to 30 seconds
        return new Request.Options(30, TimeUnit.SECONDS, 30, TimeUnit.SECONDS, true);
    }

    @Bean
    public Decoder customDecoder() {
        return (response, type) -> {
            if (response.body() == null) {
                return null;
            }
            try (var reader = response.body().asReader()) {
                // Deserialize response body into the desired type
                return objectMapper.readValue(reader, objectMapper.constructType(type));
            }
        };
    }
    
    @Bean
    public ErrorDecoder errorDecoder() {
        return new CustomErrorDecoder();
    }
    
    public static class CustomErrorDecoder implements ErrorDecoder {
        private final ErrorDecoder defaultErrorDecoder = new Default();
        
        @Override
        public Exception decode(String methodKey, Response response) {
            // Log the error for debugging purposes
            System.err.println("Error Response: " + response.status() + " for method: " + methodKey);
            
            // Use the default error decoder
            return defaultErrorDecoder.decode(methodKey, response);
        }
    }
}
