package bw.org.hrdc.weblogic.workplacelearning.entity.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@MappedSuperclass
@JsonIgnoreProperties(ignoreUnknown=true)
public class Assignment extends Auditable{

    @Column(name = "assigned_agent")
    private String assignedAgent;

    @Column(name = "assigned_agent_lead")
    private String assignedAgentLead;

    @Column(name = "assigned_officer_lead")
    private String assignedOfficerLead;

    @Column(name = "assigned_officer")
    private String assignedOfficer;

    @Column(name = "assigned_manager")
    private String assignedManager;
}
