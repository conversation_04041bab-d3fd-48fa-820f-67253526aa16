package bw.org.hrdc.weblogic.workplacelearning.dto.complaint;

import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for handling complaint actions (escalate/close)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComplaintActionRequest {
    private Enums.ComplaintState action; // ESCALATED or COMPLETED
    private String reason; // Optional reason for the action
}