package bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.*;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;

import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

public class NCBSCApplicationMapper {

    public static NCBSCApplication toEntity(NCBSCApplicationDto dto) {
        if (dto == null) {
            return null;
        }

        NCBSCApplication entity = new NCBSCApplication();
        entity.setReferenceNumber(dto.getReferenceNumber());
        entity.setApplicationNumber(dto.getApplicationNumber());
        entity.setOrganisationId(dto.getOrganisationId());
        entity.setTrainingNeedsAssessmentPurpose(dto.getTrainingNeedsAssessmentPurpose());
        entity.setTrainingNeedsAssessmentSkillsNeedsAnalysis(dto.getTrainingNeedsAssessmentSkillsNeedsAnalysis());
        entity.setShortCourseDeliveryMode(dto.getShortCourseDeliveryMode());
        entity.setKeyFacilitation(dto.getKeyFacilitation());
        entity.setAssessmentType(dto.getAssessmentType());
        entity.setCertification(dto.getCertification());
        entity.setThirdPartyArrangements(dto.getThirdPartyArrangements());
        entity.setResources(dto.getResources());
        entity.setShortCourseEndorsement(dto.getShortCourseEndorsement());
        entity.setAssignedAgent(dto.getAssignedAgent());
        entity.setAssignedAgentLead(dto.getAssignedAgentLead());
        entity.setAssignedOfficer(dto.getAssignedOfficer());
        entity.setAssignedOfficer(dto.getAssignedOfficer());
        entity.setAssignedManager(dto.getAssignedManager());
        entity.setDateSubmitted(dto.getDateSubmitted());
        entity.setApplicationStatus(dto.getApplicationStatus());
        entity.setApplicationState(dto.getApplicationState());
        entity.setManagerApprovedAt(dto.getManagerApprovedAt());

        return entity;
    }

    public static NCBSCApplicationDto toDto(NCBSCApplication entity) {
        if (entity == null) {
            return null;
        }

        NCBSCApplicationDto model = new NCBSCApplicationDto();

        model.setUuid(entity.getUuid());
        model.setReferenceNumber(entity.getReferenceNumber());
        model.setOrganisationId(entity.getOrganisationId());
        model.setTrainingNeedsAssessmentPurpose(entity.getTrainingNeedsAssessmentPurpose());
        model.setTrainingNeedsAssessmentSkillsNeedsAnalysis(entity.getTrainingNeedsAssessmentSkillsNeedsAnalysis());
        model.setShortCourseDeliveryMode(entity.getShortCourseDeliveryMode());
        model.setKeyFacilitation(entity.getKeyFacilitation());
        model.setAssessmentType(entity.getAssessmentType());
        model.setCertification(entity.getCertification());
        model.setThirdPartyArrangements(entity.getThirdPartyArrangements());
        model.setResources(entity.getResources());
        model.setShortCourseEndorsement(entity.getShortCourseEndorsement());
        model.setAssignedAgent(entity.getAssignedAgent());
        model.setAssignedAgentLead(entity.getAssignedAgentLead());
        model.setAssignedOfficer(entity.getAssignedOfficer());
        model.setAssignedOfficer(entity.getAssignedOfficer());
        model.setAssignedManager(entity.getAssignedManager());
        model.setDateSubmitted(entity.getDateSubmitted());
        model.setApplicationStatus(entity.getApplicationStatus());
        model.setApplicationState(entity.getApplicationState());
        model.setManagerApprovedAt(entity.getManagerApprovedAt());

        Set<ScopeOfAccreditationDto> scopeOfAccreditation = entity.getScopeOfAccreditation() != null
                ? ScopeOfAccreditationMapper.toDtoList(entity.getScopeOfAccreditation())
                : Collections.emptySet();

        model.setScopeOfAccreditation(scopeOfAccreditation);

        ShortCourseInformationDto shortCourseInformation = entity.getShortCourseInformation() != null
                ? ShortCourseInformationMapper.toDto(entity.getShortCourseInformation()): null;
        model.setShortCourseInformation(shortCourseInformation);


        CourseContentDeliveryDto courseContentAndDelivery = entity.getCourseContentAndDelivery() != null
                ? CourseContentDeliveryMapper.toDto(entity.getCourseContentAndDelivery(), entity.getCourseContentAndDelivery().getLearningOutcomes()) : null;
        model.setCourseContentAndDelivery(courseContentAndDelivery);

        Set<CourseDeliveryScheduleDto> courseDeliverySchedule = entity.getCourseDeliverySchedule() != null
                ? CourseDeliveryScheduleMapper.toDtoList(entity.getCourseDeliverySchedule()): Collections.emptySet();

        model.setCourseDeliverySchedule(courseDeliverySchedule);

        return model;
    }

    public static NCBSCApplicationListDto toDtoListMap(NCBSCApplication entity) {
        if (entity == null) {
            return null;
        }

        NCBSCApplicationListDto model = new NCBSCApplicationListDto();

        model.setReferenceNumber(entity.getReferenceNumber());
        model.setApplicationNumber(entity.getApplicationNumber());
        model.setOrganisationId(entity.getOrganisationId());
        model.setShortCourseDeliveryMode(entity.getShortCourseDeliveryMode());
        model.setDateSubmitted(entity.getDateSubmitted());
        model.setApplicationStatus(entity.getApplicationStatus());
        model.setApplicationState(entity.getApplicationState());

        if(entity.getApplicationState().equals(Enums.State.IN_APPROVAL) && !entity.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
            model.setAssignedTo(entity.getAssignedManager());
        }else if(entity.getApplicationState().equals(Enums.State.IN_REVIEW) && !entity.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
            model.setAssignedTo(entity.getAssignedOfficer());
        }else if(entity.getApplicationState().equals(Enums.State.IN_PROCESSING) && !entity.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
            model.setAssignedTo(entity.getAssignedAgent());
        }else if(entity.getApplicationState().equals(Enums.State.SUBMITTED) && !entity.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
            model.setAssignedTo(entity.getAssignedAgentLead());
        }else{
            model.setAssignedTo(null);
        }

        model.setCourseTitle(entity.getShortCourseInformation() != null ? entity.getShortCourseInformation().getTitle() : "");

        return model;
    }

    public static Set<NCBSCApplicationListDto> toDtoList(Set<NCBSCApplication> entity) {
        if (entity == null) {
            return null;
        }
        return entity.stream().map(NCBSCApplicationMapper::toDtoListMap).collect(Collectors.toSet());
    }
}
