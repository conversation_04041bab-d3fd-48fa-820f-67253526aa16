package bw.org.hrdc.weblogic.workplacelearning.service.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @CreatedOn 25/06/25 12:04
 * @UpdatedBy martinspectre
 * @UpdatedOn 25/06/25 12:04
 */
@Service
public class NCBSCWorkflowService {
    private final WorkflowClient workflowClient;
    private final NCBSCApplicationService applicationService;
    private final Logger logger = LoggerFactory.getLogger(NCBSCWorkflowService.class);

    public NCBSCWorkflowService(WorkflowClient workflowClient, NCBSCApplicationService applicationService) {
        this.workflowClient = workflowClient;
        this.applicationService = applicationService;
    }

    @Async("workflowExecutor")
    public void processWorkflowInBackground(NCBSCApplication recognitionApplication) {
        if (recognitionApplication.getApplicationState().equals(Enums.State.SUBMITTED)) {
            try {
                Map<String, Object> workflowResponse = workflowClient.getNCBSCApplication(
                        Enums.ApplicationType.RECOGNITION.name(),
                        recognitionApplication.getReferenceNumber()
                );

                logger.info("Workflow response: {}", workflowResponse);

                if (workflowResponse != null && Boolean.TRUE.equals(workflowResponse.get("success"))) {
                    Object data = workflowResponse.get("applicationData");
                    if (data != null) {
                        String processInstanceId = (String) workflowResponse.get("processInstanceId");
                        String referenceId = recognitionApplication.getReferenceNumber();
                        applicationService.updateProcessInstanceId(referenceId, processInstanceId);
                        logger.info("Process instance ID {} saved for application {}", processInstanceId, referenceId);
                    }
                }
            } catch (Exception e) {
                logger.error("Failed to start workflow process: {}", e.getMessage(), e);
            }
        }
    }
}
