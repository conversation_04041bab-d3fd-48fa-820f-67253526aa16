package bw.org.hrdc.weblogic.workplacelearning.dto.responsedto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * DTO for managing Request for Significant Changes.
 */
@Data
@Schema(
        name = "RequestForSignificantChanges",
        description = "Schema to hold details of requests for significant changes"
)
public class RequestForSignificantChangeDisplayDto {

    @Schema(description = "Unique identifier for the request", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @Schema(description = "Company name that made the application", example = "Weblogic")
    private String companyName;

    @Schema(description = "Name of the person assigned the application", example = "Dexter")
    private String assigneeName;

    @NotNull(message = "Request status cannot be null")
    @Schema(description = "Status of the request (e.g., Pending, Approved, Rejected)", example = "Pending")
    private String requestStatus;
    
    @NotNull(message = "Request state cannot be null")
    @Schema(description = "State of the request (e.g., Pending, Approved, Rejected)", example = "Pending")
    private String requestState;

    private String shortCourseTitle;

    private String applicationReference;

    @Schema(description = "Timestamp when the request was created", example = "2024-12-01T10:00:00")
    private Date createdAt;

}
