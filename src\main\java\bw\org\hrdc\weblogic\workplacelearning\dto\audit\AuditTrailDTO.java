package bw.org.hrdc.weblogic.workplacelearning.dto.audit;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @CreatedOn 25/02/25 21:39
 * @UpdatedBy martinspectre
 * @UpdatedOn 25/02/25 21:39
 */
@Data
@NoArgsConstructor
public class AuditTrailDTO {

    private String className;
    private String persistedObjectId;
    private String eventName;
    private String propertyName;
    private String oldValue;
    private String newValue;

    public AuditTrailDTO(String className, String persistedObjectId, String eventName, String propertyName, String oldValue, String newValue) {
        this.className = className;
        this.persistedObjectId = persistedObjectId;
        this.eventName = eventName;
        this.propertyName = propertyName;
        this.oldValue = oldValue;
        this.newValue = newValue;
    }

}
