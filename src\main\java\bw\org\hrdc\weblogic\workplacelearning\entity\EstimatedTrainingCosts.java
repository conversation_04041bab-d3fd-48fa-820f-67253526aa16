package bw.org.hrdc.weblogic.workplacelearning.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "estimated_training_costs")
@Getter
@Setter
@ToString
public class EstimatedTrainingCosts {

    @Id
    @GeneratedValue
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "pre_approval_application_id", nullable = false)
    private PreApprovalApplication preApprovalApplication;

    @Column(name = "item_description")
    private String itemDescription;

    @Column(name = "amount", nullable = false)
    private Double amount;
}
