package bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining;


import bw.org.hrdc.weblogic.workplacelearning.util.BigDecimalCommaDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * DTO for managing Course Details under the Work Place Training Plan.
 */
@Data
@Schema(
        name = "CourseDetails",
        description = "Schema to hold details of individual courses within a training plan"
)
public class CourseDetailsDto {

    @Schema(description = "Unique identifier for the course detail", example = "c2a9f21a-49cd-426b-9e89-3f0c8bcbfa01")
    private UUID id;

    @NotNull(message = "Programme name is required")
    @Schema(description = "Name of the programme", example = "Digital Marketing Fundamentals")
    private String programme;

    @NotNull(message = "Skills field is required")
    @Schema(description = "Skills to be developed in the course", example = "SEO, Content Creation")
    private String skills;

    @NotNull(message = "Training start date is required")
    @Schema(description = "Start date of the training", example = "2025-04-06")
    private LocalDate trainingStartDate;

    @NotNull(message = "Training end date is required")
    @Schema(description = "End date of the training", example = "2025-04-10")
    private LocalDate trainingEndDate;

    @NotNull(message = "Institution name is required")
    @Schema(description = "Training institution name", example = "Botswana Digital Academy")
    private String institution;

    @NotNull(message = "Location is required")
    @Schema(description = "Location where the training will take place", example = "Gaborone")
    private String location;

    @NotNull(message = "Accrediting body is required")
    @Schema(description = "Body that accredits the training", example = "HRDC")
    private String accreditingBody;

    @NotNull(message = "Level of training is required")
    @Schema(description = "Level of the training", example = "Intermediate")
    private String levelOfTraining;

    @JsonDeserialize(using = BigDecimalCommaDeserializer.class)
    @NotNull(message = "Cost of training is required")
    @Schema(description = "Total cost of the training", example = "1200.00")
    private BigDecimal costOfTraining;

    @NotNull(message = "Total number of non citizens is required")
    @Schema(description = "Total number of non citizens", example = "25")
    private Integer noncitizens;

    @NotNull(message = "Total Number of Citizens is required")
    @Schema(description = "Total number of citizens", example = "12")
    private Integer citizens;

    @NotNull(message = "People trained field is required")
    @Schema(description = "Categories of people trained", example = "Youth, Women, PWD")
    private Integer peopleTrained;

    @NotNull(message = "Course Id is required")
    @Schema(description = "Unique identifier for the approved course", example = "c2a9f21a-49cd-426b-9e89-3f0c8bcbfa01")
    private String courseId;

    @NotNull(message = "isNotLocalCourse a required")
    @Schema(description = "Identifier for local and non local courses", example = "true")
    private Boolean isNotLocalCourse;

    @Schema(description = "Identifier for courses that have been used for pre approval application", example = "true")
    private Boolean hasPreApproval;
}
