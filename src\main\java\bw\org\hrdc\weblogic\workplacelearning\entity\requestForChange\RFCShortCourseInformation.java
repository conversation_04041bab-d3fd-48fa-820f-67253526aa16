package bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.util.UUID;

@Entity
@Table(name = "rfc_short_course_information")
@EntityListeners(AuditingEntityListener.class)
@Getter
@Setter
@ToString
public class RFCShortCourseInformation extends Base {

    @Column(nullable = false)
    private String title;

    @Column(nullable = false)
    private String type;

    @Column(name = "field_of_learning")
    private String fieldOfLearning;

    @Column(name = "course_learning_time")
    private int courseLearningTime;

    private String duration;

    @Column(name = "year_due_for_review")
    private String yearDueForReview;

    @Column(name = "target_population")
    private String targetPopulation;

    @Column(name = "entry_requirements")
    private String entryRequirements;

    @OneToOne
    @JoinColumn(name = "application_id", nullable = false)
    @JsonBackReference
    @ToString.Exclude
    private RequestForSignificantChanges application;
}
