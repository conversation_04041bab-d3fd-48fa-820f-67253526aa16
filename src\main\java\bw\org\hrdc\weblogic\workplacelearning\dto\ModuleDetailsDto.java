package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.UUID;

/**
 * DTO for managing Module Details.
 */
@Data
@Schema(
        name = "ModuleDetails",
        description = "Schema to hold module details and competencies"
)
public class ModuleDetailsDto {

    @Schema(description = "Unique identifier for the module detail", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @Schema(description = "Name of the module", example = "Introduction to Machine Learning")
    private String moduleName;

    @Schema(description = "Expected competencies from the module", example = "Ability to implement ML algorithms")
    private String expectedCompetencies;

    @Schema(description = "Module reference identifier", example = "f8a9b10c-58cc-4372-a567-0e02b2c3d456")
    private UUID moduleReferenceId;
}
