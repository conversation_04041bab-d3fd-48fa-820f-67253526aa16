package bw.org.hrdc.weblogic.workplacelearning.service;

import bw.org.hrdc.weblogic.workplacelearning.dto.ComplaintDto;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.UUID;

/**
 * Service interface for managing Complaints.
 */
public interface IComplaintService {

    /**
     * Creates a new Complaint.
     *
     * @param complaintDto the DTO containing complaint details.
     */
    void createComplaint(ComplaintDto complaintDto);

    /**
     * Updates an existing Complaint.
     *
     * @param complaintDto the DTO containing updated complaint details.
     */
    void updateComplaint(ComplaintDto complaintDto);

    /**
     * Retrieves a Complaint by its ID.
     *
     * @param id the unique identifier of the complaint.
     * @return the DTO containing complaint details.
     */
    ComplaintDto getComplaint(UUID id);

    /**
     * Fetches all complaints based on search criteria and pagination.
     *
     * @param status         the status of the complaint (e.g., "Open", "Closed").
     * @param appellantName  the name of the appellant (case insensitive).
     * @param applicationId  the ID of the application related to the complaint.
     * @param organisationId the ID of the organisation associated with the complaint.
     * @param userId         the ID of the user who filed the complaint.
     * @param startDate      the start date of the submission range.
     * @param endDate        the end date of the submission range.
     * @param reasonKeyword  a keyword to search for in the reason (case insensitive).
     * @param pageable       the pagination and sorting information.
     * @return a paginated list of complaints matching the criteria.
     */
    Page<ComplaintDto> fetchAllComplaints(
            String status,
            String appellantName,
            UUID applicationId,
            UUID organisationId,
            UUID userId,
            LocalDate startDate,
            LocalDate endDate,
            String reasonKeyword,
            Pageable pageable);

    /**
     * Deletes a Complaint by its ID.
     *
     * @param id the unique identifier of the complaint to delete.
     */
    @Transactional
    void deleteComplaint(UUID id);
}