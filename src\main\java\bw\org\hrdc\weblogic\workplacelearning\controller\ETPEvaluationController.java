package bw.org.hrdc.weblogic.workplacelearning.controller;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationConstants;
import bw.org.hrdc.weblogic.workplacelearning.dto.ETPEvaluationDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.ResponseDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.ErrorResponseDto;
import bw.org.hrdc.weblogic.workplacelearning.service.IETPEvaluationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.UUID;

/**
 * REST Controller for ETP Monitoring & Evaluation.
 */
@Tag(
        name = "CRUD REST APIs for ETP Monitoring & Evaluation",
        description = "CRUD REST APIs for managing ETP Monitoring & Evaluation"
)
@RestController
@RequestMapping(path = "/api/v1/etp-evaluation", produces = {MediaType.APPLICATION_JSON_VALUE})
@Validated
public class ETPEvaluationController {

    private static final Logger logger = LoggerFactory.getLogger(ETPEvaluationController.class);

    private final IETPEvaluationService evaluationService;

    public ETPEvaluationController(IETPEvaluationService evaluationService) {
        this.evaluationService = evaluationService;
    }

    @Operation(
            summary = "Create ETP Monitoring & Evaluation REST API",
            description = "REST API to create a new ETP Monitoring & Evaluation"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "201",
                    description = "HTTP Status CREATED"
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "HTTP Status Internal Server Error",
                    content = @Content(
                            schema = @Schema(implementation = ErrorResponseDto.class)
                    )
            )
    })
    @PostMapping("/create")
    public ResponseEntity<ResponseDto> createEvaluation(@Valid @RequestBody ETPEvaluationDto evaluationDto) {
        evaluationService.createEvaluation(evaluationDto);
        return ResponseEntity
                .status(HttpStatus.CREATED)
                .body(new ResponseDto(ApplicationConstants.STATUS_200, "Evaluation created successfully."));
    }

    @Operation(
            summary = "Fetch ETP Monitoring & Evaluations REST API",
            description = "REST API to fetch ETP Monitoring & Evaluations based on filters"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "HTTP Status OK"
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "HTTP Status Internal Server Error",
                    content = @Content(
                            schema = @Schema(implementation = ErrorResponseDto.class)
                    )
            )
    })
    @GetMapping("/fetch")
    public ResponseEntity<Page<ETPEvaluationDto>> getEvaluations(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date meetingDate,
            @RequestParam(required = false) UUID userId,
            @RequestParam(required = false) UUID organisationId,
            @RequestParam(defaultValue = "0") int pageNumber,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(pageNumber, size);
        Page<ETPEvaluationDto> evaluations = evaluationService.fetchAllEvaluations(
                meetingDate, userId, organisationId, pageable);

        return ResponseEntity.ok(evaluations);
    }

    @Operation(
            summary = "Update ETP Monitoring & Evaluation REST API",
            description = "REST API to update an existing ETP Monitoring & Evaluation"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "HTTP Status OK"
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "HTTP Status Not Found",
                    content = @Content(
                            schema = @Schema(implementation = ErrorResponseDto.class)
                    )
            )
    })
    @PutMapping("/update/{id}")
    public ResponseEntity<ResponseDto> updateEvaluation(
            @PathVariable UUID id,
            @Valid @RequestBody ETPEvaluationDto evaluationDto) {
        evaluationDto.setId(id); // Ensure ID is set
        evaluationService.updateEvaluation(evaluationDto);
        return ResponseEntity.ok(
                new ResponseDto(ApplicationConstants.STATUS_200, "Evaluation updated successfully.")
        );
    }

    @Operation(
            summary = "Delete ETP Monitoring & Evaluation REST API",
            description = "REST API to delete an existing ETP Monitoring & Evaluation"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "HTTP Status OK"
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "HTTP Status Not Found",
                    content = @Content(
                            schema = @Schema(implementation = ErrorResponseDto.class)
                    )
            )
    })
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<ResponseDto> deleteEvaluation(@PathVariable UUID id) {
        evaluationService.deleteEvaluation(id);
        return ResponseEntity.ok(
                new ResponseDto(ApplicationConstants.STATUS_200, "Evaluation successfully deleted.")
        );
    }
}
