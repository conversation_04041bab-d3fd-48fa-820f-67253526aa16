package bw.org.hrdc.weblogic.workplacelearning.util;

import java.util.UUID;


/**
 * Generates an alphanumeric reference number.
 */
public class ReferenceNumberGenerator {
    /**
     * Generates an alphanumeric reference number.
     * Format: PREFIX-YYYYMMDD-RANDOM
     *
     * @param prefix a custom prefix for the reference number
     * @return generated reference number
     */
    public static String generateReferenceNumber(String prefix) {
        String date = java.time.LocalDate.now().toString().replace("-", "");
        String randomPart = UUID.randomUUID().toString().replace("-", "").substring(0, 6).toUpperCase();
        return String.format("%s-%s-%s", prefix.toUpperCase(), date, randomPart);
    }
}
