package bw.org.hrdc.weblogic.workplacelearning.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.util.UUID;

@Entity
@Table(name = "complaint")
@Getter
@Setter
@ToString
public class Complaint {

    @Id
    @GeneratedValue
    private UUID id;

    @Column(name = "appellant_name", nullable = false)
    private String appellantName;

    @Column(name = "appeal_date", nullable = false)
    private LocalDate appealDate;

    @Column(name = "reason", nullable = false)
    private String reason;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "date_submitted", nullable = false)
    private LocalDate dateSubmitted;

    @Column(name = "application_id")
    private UUID applicationId;

    @Column(name = "application_type")
    private String applicationType;

    @Column(name = "date_reviewed")
    private LocalDate dateReviewed;

    @Column(name = "date_responded")
    private LocalDate dateResponded;

    @Column(name = "date_closed")
    private LocalDate dateClosed;

    @Column(name = "user_id", nullable = false)
    private UUID userId;

    @Column(name = "organisation_id", nullable = false)
    private UUID organisationId;

}