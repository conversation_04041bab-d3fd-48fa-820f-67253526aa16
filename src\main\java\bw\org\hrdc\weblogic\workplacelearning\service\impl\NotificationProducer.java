package bw.org.hrdc.weblogic.workplacelearning.service.impl;

import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import org.springframework.kafka.core.KafkaTemplate;

@Service
@RequiredArgsConstructor
public class NotificationProducer {
    private static final Logger log = LoggerFactory.getLogger(NotificationProducer.class);

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;


    public void sendApplicationNotification(String topic, String applicationNumber, String applicationStatus) {
        Map<String, Object> message = new HashMap<>();
        message.put("applicationNumber", applicationNumber);
        message.put("applicationStatus", applicationStatus);
        message.put("timestamp", LocalDateTime.now().toString());
        message.put("source", "workflow-service");
        message.put("eventType", "APPLICATION_STATUS_CHANGE");

        try {
            // Convert the map to a JSON string
            String messageJson = objectMapper.writeValueAsString(message);
            
            // Check if Kafka is available before sending
            if (!isKafkaAvailable()) {
                log.warn("Kafka is not available. Skipping notification for application: {}", applicationNumber);
                return;
            }
            
            // Use application number as the key for message partitioning
            java.util.concurrent.CompletableFuture<SendResult<String, String>> future =
                kafkaTemplate.send(topic, applicationNumber, messageJson);

            // Add callback to handle success/failure
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    log.info("Sent message=[{}] with offset=[{}] to topic=[{}]",
                        messageJson, result.getRecordMetadata().offset(), topic);
                } else {
                    log.error("Unable to send message=[{}] to topic=[{}] due to : {}",
                        messageJson, topic, ex.getMessage());
                }
            });
            
        } catch (JsonProcessingException e) {
            log.error("Error serializing notification message: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("Error sending notification to Kafka: {}", e.getMessage(), e);
        }
    }
    

    private boolean isKafkaAvailable() {
        try {
            // Attempt to send a lightweight message to a health-check topic
            kafkaTemplate.send("kafka-health-check", "ping").get();
            return true;
        } catch (Exception e) {
            log.warn("Kafka connection check failed: {}", e.getMessage());
            return false;
        }
    }
}
