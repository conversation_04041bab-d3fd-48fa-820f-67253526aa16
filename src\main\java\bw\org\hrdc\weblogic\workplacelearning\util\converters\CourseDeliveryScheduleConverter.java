package bw.org.hrdc.weblogic.workplacelearning.util.converters;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.CourseDeliverySchedule;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;

import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR>
 * @CreatedOn 14/04/25 10:17
 * @UpdatedBy martinspectre
 * @UpdatedOn 14/04/25 10:17
 */
public class CourseDeliveryScheduleConverter  implements AttributeConverter<Set<CourseDeliverySchedule>, String> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String convertToDatabaseColumn(Set<CourseDeliverySchedule> attribute) {
        try {
            return objectMapper.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting set to JSON", e);
        }
    }

    @Override
    public Set<CourseDeliverySchedule> convertToEntityAttribute(String dbData) {
        try {
            JavaType type = objectMapper.getTypeFactory().constructCollectionType(Set.class, CourseDeliverySchedule.class);
            return objectMapper.readValue(dbData, type);
        } catch (IOException e) {
            throw new RuntimeException("Error reading JSON from DB", e);
        }
    }
}