package bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc;

import lombok.Data;

/**
 * <AUTHOR>
 * @CreatedOn 20/02/25 19:34
 * @UpdatedBy martinspectre
 * @UpdatedOn 20/02/25 19:34
 */
@Data
public class ApplicationSearchCriteria {
    private String status;
    private String state;
    private String sortBy = "dateSubmitted";  // Default sort by submission date
    private String direction = "DESC";        // Default newest first
    private String assignedTo;
    private String courseTitle;
    private String startDate;
    private String endDate;
    private String referenceNumber;
    private String[] sortFields;              // Multiple sort fields
}
