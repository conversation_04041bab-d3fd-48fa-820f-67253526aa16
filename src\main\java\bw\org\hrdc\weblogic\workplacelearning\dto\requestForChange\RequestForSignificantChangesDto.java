package bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * DTO for managing Request for Significant Changes.
 */
@Data
@Schema(
        name = "RequestForSignificantChanges",
        description = "Schema to hold details of requests for significant changes"
)
public class RequestForSignificantChangesDto {

    @Schema(description = "Unique identifier for the request", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID uuid;

    @Schema(description = "Organisation ID", example = "8dff3b1e-f792-4df5-a3c7-2190f4d3a623")
    private UUID organisationId;

    @Schema(description = "NonCreditBearingShortCourse Id")
    private UUID ncbscApplicationId;

    @Schema(description = "Assigned user's ID", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID assignedTo;

    @NotNull(message = "Title cannot be null")
    @Schema(description = "Title of the request", example = "Change in Course Title")
    private String title;

    @Schema(description = "Justification for the requested change", example = "The course title does not reflect industry standards.")
    private String justification;

    @Schema(description = "Name of the person requesting the change", example = "John Doe")
    private String requestedBy;

    @Schema(description = "Assessment Purpose", example = "Identify skill gaps in IT")
    private String assessmentPurpose;

    @Schema(description = "Skills Needs Analysis", example = "Survey-based analysis of existing workforce skills")
    private String skillsNeedsAnalysis;

    @Schema(description = "Short Course Delivery Mode", example = "Online")
    private String shortCourseDeliveryMode;

    @Schema(description = "Key Facilitation", example = "Experienced trainers with relevant certifications")
    private String keyFacilitation;

    @Schema(description = "Skills Assessment", example = "Practical exercises and projects")
    private String skillsAssessment;

    @Schema(description = "Assessment Type", example = "Multiple-choice tests and project evaluations")
    private String assessmentType;

    @Schema(description = "Certification", example = "Certificate of completion")
    private String certification;

    @Schema(description = "Resources", example = "Resources needed for the course")
    private String resources;

    @Schema(description = "Third Party Arrangements", example = "Collaboration with ABC Institute")
    private String thirdPartyArrangements;

    @Schema(description = "Short Course Endorsement", example = "Course Endorsement")
    private String shortCourseEndorsement;

    @Schema(description = "Scope of Accreditation")
    private List<String> scopeOfAccreditation;

    @Schema(description = "Short Course Information")
    private RFCShortCourseInformationDto shortCourseInformation;

    @Schema(description = "Course Content and Delivery")
    private RFCCourseContentAndDeliveryDto courseContentAndDelivery;

    @Schema(description = "List of Learning Outcomes")
    private List<RFCLearningOutcomeDto> learningOutcomes;

    @Schema(description = "List of Course Delivery Schedules")
    private List<RFCCourseDeliveryScheduleDto> courseDeliverySchedule;

    @Schema(description = "List of details associated with the significant changes")
    private List<RFCDetailsOfSignificantChangeDto> detailsOfSignificantChanges;

    @NotNull(message = "Request status cannot be null")
    @Schema(description = "Status of the request (e.g., Pending, Approved, Rejected)", example = "Pending")
    private String applicationStatus;

    @NotNull(message = "Request state cannot be null")
    @Schema(description = "State of the request (e.g., Pending, Approved, Rejected)", example = "Inprogress")
    private String applicationState;

    @Schema(description = "Reference Number of the request", example = "ref-202500123")
    private String referenceNumber;

    @Schema(description = "Timestamp when the request was created", example = "2024-12-01T10:00:00")
    private Date createdAt;

    @Schema(description = "Username of the person who created the request", example = "jdoe")
    private String createdBy;

    @Schema(description = "Timestamp when the request was last updated", example = "2024-12-02T15:00:00")
    private Date updatedAt;

    @Schema(description = "Username of the person who last updated the request", example = "jdoe")
    private String updatedBy;

    @Schema(description = "Date Submitted", example = "2024-12-01T00:00:00.000+00:00")
    private Date dateSubmitted = new Date();

    private boolean isChangeOfTitle;

    private boolean isChangeOfCourseDuration;

    private boolean isChangeOfCoreComponents;

    private boolean isChangeOfPartnerships;

    private boolean isChangeOfModeOfDelivery;

    private String assignedAgent;

    private String assignedAgentLead;

    private String assignedOfficerLead;

    private String assignedOfficer;

    private String assignedManager;
}
