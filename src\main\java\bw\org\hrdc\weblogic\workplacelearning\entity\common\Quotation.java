package bw.org.hrdc.weblogic.workplacelearning.entity.common;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Auditable;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @CreatedOn 15/04/25 09:14
 * @UpdatedBy martinspectre
 * @UpdatedOn 15/04/25 09:14
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "quotations")
@Getter
@Setter
public class Quotation extends Auditable implements Serializable {
    private String quoteRef;
    private String reference;
    @Lob
    private String checklistItemsJson;
    private double totalAmount;
    private boolean accepted;
}
