package bw.org.hrdc.weblogic.workplacelearning.mapper.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.RFCShortCourseInformationDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RFCShortCourseInformation;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RequestForSignificantChanges;
import org.springframework.stereotype.Component;

@Component
public class RFCShortCourseInformationMapper {

    public static RFCShortCourseInformationDto toDto(RFCShortCourseInformation entity) {
        if (entity == null) {
            return null;
        }

        RFCShortCourseInformationDto dto = new RFCShortCourseInformationDto();
        dto.setTitle(entity.getTitle());
        dto.setType(entity.getType());
        dto.setFieldOfLearning(entity.getFieldOfLearning());
        dto.setCourseLearningTime(entity.getCourseLearningTime());
        dto.setDuration(entity.getDuration());
        dto.setYearDueForReview(entity.getYearDueForReview());
        dto.setTargetPopulation(entity.getTargetPopulation());
        dto.setEntryRequirements(entity.getEntryRequirements());

        return dto;
    }

    public static RFCShortCourseInformation toEntity(RFCShortCourseInformationDto dto, RequestForSignificantChanges application ) {
        if (dto == null) {
            return null;
        }

        RFCShortCourseInformation entity = new RFCShortCourseInformation();
        entity.setApplication(application); // Set parent reference
        entity.setTitle(dto.getTitle());
        entity.setType(dto.getType());
        entity.setFieldOfLearning(dto.getFieldOfLearning());
        entity.setCourseLearningTime(dto.getCourseLearningTime());
        entity.setDuration(dto.getDuration());
        entity.setYearDueForReview(dto.getYearDueForReview());
        entity.setTargetPopulation(dto.getTargetPopulation());
        entity.setEntryRequirements(dto.getEntryRequirements());

        return entity;
    }

}