package bw.org.hrdc.weblogic.workplacelearning.service;

import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplicationComments;

import java.util.List;
import java.util.UUID;

public interface PreApprovalApplicationCommentsService {
    void createComments(PreApprovalApplicationComments comments);
    
    /**
     * Retrieves all audit logs/comments for a specific application.
     *
     * @param applicationId the ID of the application
     * @return list of comments/audit logs for the application
     */
    List<PreApprovalApplicationComments> getAuditLogsForApplication(UUID applicationId);
}
