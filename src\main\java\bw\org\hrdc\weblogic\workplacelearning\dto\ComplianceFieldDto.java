package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * DTO for fields within compliance criteria in ETP evaluation.
 */
@Data
@Schema(
        name = "ComplianceField",
        description = "Schema to hold details of a field in compliance criteria"
)
public class ComplianceFieldDto {

    @Schema(description = "Label describing the field", example = "ETP Accreditation and Scope of Accreditation")
    private String label;

    @Schema(description = "Comments on the field", example = "Accreditation is valid and comprehensive.")
    private String comments;

    @Schema(description = "Observations made during the evaluation", example = "The scope of accreditation is well-defined.")
    private String observations;

    @Schema(description = "Compliance status for the field", example = "true")
    private Boolean compliant;
}
