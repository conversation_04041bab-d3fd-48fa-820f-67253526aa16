package bw.org.hrdc.weblogic.workplacelearning.dto.complaint;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DTO containing information about who rejected the original application
 * Used in appeal creation response to provide context about the rejection
 */
@Data
@Builder
public class ApplicationRejectionInfo {
    private String typeOfApplication;    // Type of application (PRE_APPROVAL, RECOGNITION, etc.)
    private String status;               // Status of original application (REJECTED)
    private String rejectedBy;           // Role of who rejected (AGENT, OFFICER, MANAGER)
    private String rejectedBy_userid;    // User ID of the person who rejected the application
}
