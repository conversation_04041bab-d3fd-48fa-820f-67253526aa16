package bw.org.hrdc.weblogic.workplacelearning.mapper;

import bw.org.hrdc.weblogic.workplacelearning.dto.DocumentDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.Document;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between Document entity and DocumentDto.
 */
@Component
public class DocumentMapper {

    private final ModelMapper modelMapper;

    @Autowired
    public DocumentMapper(ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
    }

    // Convert Entity to DTO
    public DocumentDto toDto(Document entity) {
        return modelMapper.map(entity, DocumentDto.class);
    }

    // Convert DTO to Entity
    public Document toEntity(DocumentDto dto) {
        return modelMapper.map(dto, Document.class);
    }

    // Convert Page<Document> to Page<DocumentDto>
    public Page<DocumentDto> toDtoPage(Page<Document> entityPage) {
        return entityPage.map(this::toDto);
    }

    // Convert List<Document> to List<DocumentDto>
    public List<DocumentDto> toDtoList(List<Document> entityList) {
        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
