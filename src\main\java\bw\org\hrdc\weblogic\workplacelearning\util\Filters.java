package bw.org.hrdc.weblogic.workplacelearning.util;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @CreatedOn 08/06/25 08:55
 * @UpdatedBy martinspectre
 * @UpdatedOn 08/06/25 08:55
 */
public class Filters <T>{

    public List<Predicate> buildDateRangePredicates(CriteriaBuilder cb, Root<T> root, Date startDate, Date endDate) {
        List<Predicate> predicates = new ArrayList<>();
        ZoneId zone = ZoneId.systemDefault();

        if (startDate != null || endDate != null) {
            LocalDateTime start = startDate != null ? startDate.toInstant().atZone(zone).toLocalDateTime().withHour(0).withMinute(0).withSecond(0) : null;
            LocalDateTime end = endDate != null ? endDate.toInstant().atZone(zone).toLocalDateTime().withHour(23).withMinute(59).withSecond(59) : null;

            Date from = start != null ? Date.from(start.atZone(zone).toInstant()) : null;
            Date to = end != null ? Date.from(end.atZone(zone).toInstant()) : null;

            if (from != null && to != null) {
                predicates.add(cb.between(root.get("createdDate"), from, to));
            } else if (from != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("createdDate"), from));
            } else {
                predicates.add(cb.lessThanOrEqualTo(root.get("createdDate"), to));
            }
        }

        return predicates;
    }

    public List<Predicate> buildApplicationStatusPredicate(CriteriaBuilder cb, Root<T> root, String status) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(cb.or(cb.like(cb.lower(root.get("applicationStatus")), "%" + status.trim().toLowerCase() + "%")));
        return predicates;
    }

    public List<Predicate> buildApplicationStatePredicate(CriteriaBuilder cb, Root<T> root, String state) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(cb.or(cb.like(cb.lower(root.get("applicationState")), "%" + state.trim().toLowerCase() + "%")));

        return predicates;
    }

    public List<Predicate> referenceNumberPredicate(CriteriaBuilder cb, Root<T> root, String refNumber) {
        List<Predicate> predicates = new ArrayList<>();
        if (refNumber != null && !refNumber.trim().isEmpty()) {
            predicates.add(cb.like(cb.lower(root.get("referenceNumber")), "%" + refNumber.trim().toLowerCase() + "%"));
        }
        return predicates;
    }

    public List<Predicate> applicationNumberPredicate(CriteriaBuilder cb, Root<T> root, String refNumber) {
        List<Predicate> predicates = new ArrayList<>();
        if (refNumber != null && !refNumber.trim().isEmpty()) {
            predicates.add(cb.like(cb.lower(root.get("applicationNumber")), "%" + refNumber.trim().toUpperCase() + "%"));
        }
        return predicates;
    }

    public List<Predicate> buildSubmissionDatePredicate(CriteriaBuilder cb, Root<T> root, Date submissionDate) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(cb.equal(root.get("submissionDate"), submissionDate));

        return predicates;

    }

    public Specification<T> buildBaseRoleSpecification(String role, UUID userId, UUID organisationId) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.isFalse(root.get("isDeleted")));

            if (organisationId != null) {
                predicates.add(cb.equal(root.get("organisationId"), organisationId.toString()));
            }

            if (userId != null) {
                predicates.add(cb.equal(root.get("createdBy"), userId.toString()));

                String userIdStr = userId.toString();
                String stateField = "applicationState";

                predicates.add(cb.notEqual(root.get(stateField), "DRAFT"));

            }

            if (role != null) {
                switch (role.toUpperCase()) {
                    case "AGENT":
                        predicates.add(cb.equal(root.get("assignedAgent"), role.toUpperCase()));
                        break;
                    case "OFFICER":
                        predicates.add(cb.equal(root.get("assignedOfficer"), role.toUpperCase()));
                        break;
                    case "OFFICER_LEAD":
                        predicates.add(cb.equal(root.get("officerLead"), "IN_REVIEW"));
                        break;
                    case "MANAGER":
                        predicates.add(cb.equal(root.get("Manager"), "IN_APPROVAL"));
                        break;
                    default:
                        predicates.add(cb.equal(cb.literal(1), 0)); // Return no results
                }
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    private List<Predicate> buildCompanyPredicate(CriteriaBuilder cb, Root<T> root, List<Map<String, Object>> companies, String searchData) {
        List<Predicate> predicates = new ArrayList<>();

        try {
            List<UUID> companyIds = companies.stream()
                    .filter(c -> {
                        try {
                            return c.get("organizationId").equals(searchData);
                        } catch (Exception e) {
                            return false;
                        }
                    })
                    .map(c -> {
                        try {
                            return UUID.fromString(c.get("organizationId").toString());
                        } catch (Exception e) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (!companyIds.isEmpty()) {
                predicates.add(root.get("organisationId").in(companyIds));
            } else {
                predicates.add(cb.equal(cb.literal(1), 0));
            }
        } catch (Exception e) {
            predicates.add(cb.equal(cb.literal(1), 0));
        }

        return predicates;
    }

}
