package bw.org.hrdc.weblogic.workplacelearning.service.document;

import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.entity.document.DocumentCommon;
import bw.org.hrdc.weblogic.workplacelearning.repository.document.DocumentRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 23/06/25 12:05
 * @UpdatedBy martinspectre
 * @UpdatedOn 23/06/25 12:05
 */
@Service
@RequiredArgsConstructor
public class DocumentService {
    private static final Logger logger = LoggerFactory.getLogger(DocumentService.class);

    private final DocumentRepository documentRepository;
    @Autowired
    private CompanyClient companyClient;

    @Transactional()
    public DocumentCommon saveDocument(DocumentCommon document) {
        return documentRepository.save(document);
    }

    public List<DocumentCommon> fetchDocuments(String identify) {
        return documentRepository.findByIdentifier(identify);
    }
}
