package bw.org.hrdc.weblogic.workplacelearning.mapper;

import bw.org.hrdc.weblogic.workplacelearning.dto.TrainingDetailDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.TrainingDetail;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between TrainingDetail entity and TrainingDetailDto.
 */
@Component
public class TrainingDetailMapper {

    private final ModelMapper modelMapper;

    @Autowired
    public TrainingDetailMapper(ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
    }

    /**
     * Convert Entity to DTO.
     */
    public TrainingDetailDto toDto(TrainingDetail entity) {
        return modelMapper.map(entity, TrainingDetailDto.class);
    }

    /**
     * Convert DTO to Entity.
     */
    public TrainingDetail toEntity(TrainingDetailDto dto) {
        return modelMapper.map(dto, TrainingDetail.class);
    }

    /**
     * Convert a List of TrainingDetail entities to a List of TrainingDetailDto.
     */
    public List<TrainingDetailDto> toDtoList(List<TrainingDetail> entityList) {
        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert a List of TrainingDetailDto to a List of TrainingDetail entities.
     */
    public List<TrainingDetail> toEntityList(List<TrainingDetailDto> dtoList) {
        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
