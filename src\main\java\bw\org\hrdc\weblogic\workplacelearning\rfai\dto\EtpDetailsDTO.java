package bw.org.hrdc.weblogic.workplacelearning.rfai.dto;

import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class EtpDetailsDTO {
    @NotBlank
    private String nameOfEducationProvider;

    @NotBlank
    private String registrationAccreditationNumber;

    @NotBlank
    private String courseTitle;

    @NotBlank
    private String fieldNumber;

    private Enums.EtpCategory categoryOfETP = Enums.EtpCategory.Conventional;
}
