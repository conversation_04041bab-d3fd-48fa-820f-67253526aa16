package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.UUID;

/**
 * DTO for managing Resource details.
 */
@Data
@Schema(
        name = "Resource",
        description = "Schema to hold Resource details"
)
public class ResourceDto {

    @Schema(description = "Unique identifier for the resource", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @NotNull(message = "Description cannot be null")
    @Schema(description = "Description of the resource", example = "Laptop with a minimum of 8GB RAM and 256GB SSD")
    private String description;

    @NotNull(message = "Non-Credit Bearing Course ID cannot be null")
    @Schema(description = "The ID of the associated Non-Credit Bearing Course", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID nonCreditBearingCourseId;
}
