package bw.org.hrdc.weblogic.workplacelearning.dto.responsedto;

import lombok.Data;

@Data
public class CompanyApiResponse {
    private boolean status;
    private String message;
    private CompanyData data;
    private Object errors;

    @Data
    public static class CompanyData {
        private String uuid;
        private String name;
    }

    public boolean getStatus() {
        return status;
    }
}