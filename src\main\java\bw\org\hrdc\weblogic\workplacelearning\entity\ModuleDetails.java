package bw.org.hrdc.weblogic.workplacelearning.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "module_details")
@Getter
@Setter
@ToString
public class ModuleDetails {

    @Id
    @GeneratedValue
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "pre_approval_application_id", nullable = false)
    private PreApprovalApplication preApprovalApplication;

    @Column(name = "module_name")
    private String moduleName;

    @Column(name = "expected_competencies")
    private String expectedCompetencies;

    @Column(name = "module_reference_id")
    private UUID moduleReferenceId;
}

