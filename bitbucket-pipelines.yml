image: atlassian/default-image:2  # Use the default Atlassian Docker image

pipelines:
  branches:
    develop:  # Define the pipeline for the 'develop' branch
      - step:
          name: Build, Tag, Push, and Deploy Docker Image  # Step name
          deployment: development  # Specify the deployment environment
          services:
            - docker  # Enable Docker service for this step
          script:
            - docker build -t $IMAGE_NAME -f Dockerfile .  # Build the Docker image using the specified Dockerfile
            - echo $DOCKERHUB_PASSWORD | docker login -u $DOCKERHUB_USERNAME --password-stdin  # Log in to Docker Hub
            - docker push $IMAGE_NAME  # Push the Docker image to Docker Hub
            - mkdir -p ~/.ssh  # Create an SSH directory
            - echo "$DEPLOY_SSH_KEY" > ~/.ssh/id_rsa  # Save the deployment SSH key
            - chmod 600 ~/.ssh/id_rsa   # Set permissions for the SSH key
            - ssh-keyscan $SERVER >> ~/.ssh/known_hosts   # Add the server to known hosts to prevent SSH warnings
            - ssh -v -i ~/.ssh/id_rsa $SERVER_USER@$SERVER "cd deployment && docker-compose pull && docker-compose up -d"