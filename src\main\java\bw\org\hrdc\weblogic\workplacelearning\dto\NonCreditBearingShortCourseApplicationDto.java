package bw.org.hrdc.weblogic.workplacelearning.dto;

import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.RFCCourseDeliveryScheduleDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.RFCShortCourseInformationDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Set;
import java.util.UUID;

/**
 * DTO for managing Non-Credit Bearing Short Course Application details.
 */
@Data
@Schema(
        name = "NonCreditBearingShortCourseApplication",
        description = "Schema to hold Non-Credit Bearing Short Course Application details"
)
public class NonCreditBearingShortCourseApplicationDto {

    @Schema(description = "Unique identifier for the application", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @Schema(description = "Unique identifier for the user submitting the application", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID userId;

    @NotNull(message = "Organization ID cannot be null")
    @Schema(description = "Unique identifier for the organization", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID organisationId;

    @Schema(description = "Unique identifier for the user who assigned the application", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID assignedBy;

    @Schema(description = "Unique identifier for the user who was assigned the application", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID assignedTo;

    @Schema(description = "Mode of short course delivery", example = "Online")
    private String shortCourseDeliveryMode;

    @Schema(description = "Application status", example = "APPROVED")
    private String applicationStatus;

    @Schema(description = "Key facilitation details", example = "Experienced trainers")
    private String keyFacilitation;

    @Schema(description = "Skills assessment details", example = "Practical coding assessment")
    private String skillsAssessment;

    @Schema(description = "Certification assessment details", example = "Multiple-choice exam")
    private String certificationAssessment;

    @Schema(description = "Third-party arrangements", example = "Partnership with XYZ Institute")
    private String thirdPartyArrangements;

    @Schema(description = "Set of scope of accreditation details")
    private Set<ScopeOfAccreditationDto> scopeOfAccreditation;

    @Schema(description = "Set of Learning Outcome Topics")
    private Set<LearningOutcomeTopicDto> learningOutcomeTopic;

    @Schema(description = "Training needs assessment details")
    private TrainingNeedsAssessmentDto trainingNeedsAssessment;

    @Schema(description = "Short course information details")
    private RFCShortCourseInformationDto shortCourseInformation;


    @Schema(description = "Set of course delivery schedule details")
    private Set<RFCCourseDeliveryScheduleDto> courseDeliverySchedule;
}
