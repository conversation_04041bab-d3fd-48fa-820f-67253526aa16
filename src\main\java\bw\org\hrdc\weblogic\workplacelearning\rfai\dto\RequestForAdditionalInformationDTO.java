package bw.org.hrdc.weblogic.workplacelearning.rfai.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public class RequestForAdditionalInformationDTO {
    @NotBlank
    private String organisationId;

    @NotBlank
    private String submittedByUserId;

    @NotNull
    @Valid
    private EtpDetailsDTO etpDetails;

    @Valid
    private List<AdditionalInfoRequestDTO> additionalInformationRequired;
}
