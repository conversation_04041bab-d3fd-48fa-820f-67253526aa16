package bw.org.hrdc.weblogic.workplacelearning.audit;

import bw.org.hrdc.weblogic.workplacelearning.dto.audit.AuditTrailDTO;
import bw.org.hrdc.weblogic.workplacelearning.service.audit.AuditLogService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApplicationContextProvider;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import org.hibernate.CallbackException;
import org.hibernate.EmptyInterceptor;
import org.hibernate.type.Type;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 25/02/25 21:34
 * @UpdatedBy martinspectre
 * @UpdatedOn 25/02/25 21:34
 */
//TODO remove logging here
@Component
public class AuditLogInterceptor extends EmptyInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(AuditLogInterceptor.class);

    @Override
    public boolean onFlushDirty(Object entity, Serializable id, Object[] currentState, Object[] previousState, String[] propertyNames, Type[] types) throws CallbackException {
        if (entity instanceof AuditAware) {
            List<AuditTrailDTO> auditTrailDTOList = new ArrayList<>();
            AuditLogService auditLogService = (AuditLogService) ApplicationContextProvider.getApplicationContext().getBean("auditLogService");
            for (int i = 0; i < currentState.length; i++) {
                if (!previousState[i].equals(currentState[i])) {
                    logger.info("Inside On Flush Dirty   ************    **************      ==>>      {}", propertyNames[i]);
                    auditTrailDTOList.add(new AuditTrailDTO(entity.getClass().getCanonicalName(), id.toString(), Enums.AuditEvent.UPDATE.name(), propertyNames[i], previousState[i].toString(), currentState[i].toString()));
                }
            }
            auditTrailDTOList.forEach(auditLogService::save);
        }
        return true;
    }

    @Override
    public boolean onSave(Object entity, Serializable id, Object[] state, String[] propertyNames, Type[] types) throws CallbackException {
        if (entity instanceof AuditAware) {
            List<AuditTrailDTO> auditTrailDTOList = new ArrayList<>();
            AuditLogService auditLogService = (AuditLogService) ApplicationContextProvider.getApplicationContext().getBean("auditLogService");
            for (int i = 0; i < propertyNames.length; i++) {
                logger.info("Inside On Save   ************    ************** ===>>>      {}", propertyNames[i]);
                auditTrailDTOList.add(new AuditTrailDTO(entity.getClass().getCanonicalName(), id.toString(), Enums.AuditEvent.INSERT.name(), propertyNames[i], null, state[i].toString()));
            }
            auditTrailDTOList.forEach(auditLogService::save);
        }
        return super.onSave(entity, id, state, propertyNames, types);
    }

    @Override
    public void onDelete(Object entity, Serializable id, Object[] state, String[] propertyNames, Type[] types) throws CallbackException {
        if (entity instanceof AuditAware) {
            List<AuditTrailDTO> auditTrailDTOList = new ArrayList<>();
            AuditLogService auditLogService = (AuditLogService) ApplicationContextProvider.getApplicationContext().getBean("auditLogService");
            for (int i = 0; i < propertyNames.length; i++) {
                logger.info("Inside On Delete   ************    ************** ===>>>      {}", propertyNames[i]);
                auditTrailDTOList.add(new AuditTrailDTO(entity.getClass().getCanonicalName(), id.toString(), Enums.AuditEvent.DELETE.name(), propertyNames[i], state[i].toString(), null));
            }
            auditTrailDTOList.forEach(auditLogService::save);
        }
    }

}
