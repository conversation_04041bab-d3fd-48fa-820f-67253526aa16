package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.UUID;

/**
 * DTO for managing Particulars of Trainees.
 */
@Data
@Schema(
        name = "ParticularsOfTrainees",
        description = "Schema to hold trainee details"
)
public class ParticularsOfTraineesDto {

    @Schema(description = "Unique identifier for the trainee", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @Schema(description = "Surname of the trainee", example = "Doe")
    private String surname;

    @Schema(description = "First name of the trainee", example = "<PERSON>")
    private String firstName;

    @Schema(description = "Passport or Omang number", example = "*********")
    private String passportOrOmangNo;

    @Schema(description = "Gender of the trainee", example = "Male")
    private String gender;
}
