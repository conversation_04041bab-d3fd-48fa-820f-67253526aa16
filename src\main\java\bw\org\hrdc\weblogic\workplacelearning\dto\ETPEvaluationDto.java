package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * DTO for managing ETP Monitoring & Evaluation details.
 */
@Data
@Schema(
        name = "ETPEvaluation",
        description = "Schema to hold ETP Monitoring & Evaluation details"
)
public class ETPEvaluationDto {

    @Schema(description = "Unique identifier for the evaluation", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @Schema(description = "Unique identifier for the user conducting the evaluation", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID userId;

    @NotNull(message = "Organization ID cannot be null")
    @Schema(description = "Unique identifier for the organization being evaluated", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID organisationId;

    @Schema(description = "Date of the evaluation meeting", example = "2024-12-01")
    private Date meetingDate;

    @Schema(description = "List of discussion items covered during the evaluation")
    private List<DiscussionItemDto> discussionItems;

    @Schema(description = "List of compliance criteria and observations")
    private List<ComplianceCriteriaDto> complianceCriteria;

    @Schema(description = "Summary of the assessment", example = "The evaluation highlighted key strengths and areas for improvement.")
    private String assessmentSummary;

    @Schema(description = "Action plan for addressing non-conformance issues")
    private List<ActionPlanDto> actionPlans;

    @Schema(description = "Progress report on corrective actions")
    private List<CorrectiveActionProgressReportDto> correctiveActionProgressReports;
}
