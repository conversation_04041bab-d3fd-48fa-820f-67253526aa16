package bw.org.hrdc.weblogic.workplacelearning.rfai.dto;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import bw.org.hrdc.weblogic.workplacelearning.rfai.entity.AdditionalInformation;
import lombok.Data;

import java.util.List;

@Data
public class RequestForAdditionalInformationRequest {
    private String organisationId;
    private String submittedByUserId;
    private ApplicationStatus status;
    private EtpDetailsDTO etpDetails;
    private List<AdditionalInformationRequest> additionalInformationRequired;
}
