package bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.document.FileDocumentDto;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @CreatedOn 18/02/25 20:05
 * @UpdatedBy martinspectre
 * @UpdatedOn 18/02/25 20:05
 */
@Data
public class NCBSCApplicationDto{
    private Long id;
    private String uuid;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;
    private boolean isDeleted = false;
    private String referenceNumber;
    private String applicationNumber;
    private String organisationId;
    private String trainingNeedsAssessmentPurpose;
    private String trainingNeedsAssessmentSkillsNeedsAnalysis;
    private String shortCourseDeliveryMode;
    private String keyFacilitation;
    private String assessmentType;
    private String certification;
    private String thirdPartyArrangements;
    private String resources;
    private String shortCourseEndorsement;
    private String assignedAgent;
    private String assignedAgentLead;
    private String assignedOfficerLead;
    private String assignedOfficer;
    private String assignedManager;
    private Set<ScopeOfAccreditationDto> scopeOfAccreditation;
    private ShortCourseInformationDto shortCourseInformation;
    private CourseContentDeliveryDto courseContentAndDelivery;
    private Set<CourseDeliveryScheduleDto> courseDeliverySchedule;
    private List<FileDocumentDto> attachments;
    private LocalDateTime dateSubmitted;
    private Enums.Status applicationStatus;
    private Enums.State applicationState;
    private String processInstanceId;
    private LocalDateTime managerApprovedAt;


}