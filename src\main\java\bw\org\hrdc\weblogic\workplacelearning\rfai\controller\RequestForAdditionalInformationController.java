package bw.org.hrdc.weblogic.workplacelearning.rfai.controller;

import bw.org.hrdc.weblogic.workplacelearning.rfai.dto.RequestForAdditionalInformationRequest;
import bw.org.hrdc.weblogic.workplacelearning.rfai.dto.RequestForAdditionalInformationResponse;
import bw.org.hrdc.weblogic.workplacelearning.rfai.service.RequestForAdditionalInformationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@RestController
@RequestMapping("/additional_information")
@Tag(name = "Request For Additional Information", description = "APIs for managing Request For Additional Information")
public class RequestForAdditionalInformationController {
    private final RequestForAdditionalInformationService requestForAdditionalInformationService;

    public RequestForAdditionalInformationController(RequestForAdditionalInformationService requestForAdditionalInformationService) {
        this.requestForAdditionalInformationService = requestForAdditionalInformationService;
    }

    @PostMapping
    @Operation(summary = "Create a newAdditional Information")
    public ResponseEntity<RequestForAdditionalInformationResponse> createAdditionalInformation(@RequestBody RequestForAdditionalInformationRequest request) {
        RequestForAdditionalInformationResponse response = requestForAdditionalInformationService.createAdditionalInformation(request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @GetMapping
    @Operation(summary = "Get all Additional Information")
    public ResponseEntity<List<RequestForAdditionalInformationResponse>> getAllAdditionalInformation() {
        return ResponseEntity.ok(requestForAdditionalInformationService.getAllAdditionalInformation());
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get an Additional Information by ID")
    public ResponseEntity<RequestForAdditionalInformationResponse> getAdditionalInformationById(@PathVariable Long id) {
        return ResponseEntity.ok(requestForAdditionalInformationService.getAdditionalInformationById(id));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update an existing Additional Information")
    public ResponseEntity<RequestForAdditionalInformationResponse> updateAdditionalInformation(
            @PathVariable Long id, @RequestBody RequestForAdditionalInformationRequest request) {
        return ResponseEntity.ok(requestForAdditionalInformationService.updateAdditionalInformation(id, request));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete an Additional Information")
    public ResponseEntity<Void> deleteApplication(@PathVariable Long id) {
        requestForAdditionalInformationService.deleteApplication(id);
        return ResponseEntity.noContent().build();
    }
}
