package bw.org.hrdc.weblogic.workplacelearning.api;

import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
public class CompanyClientFallback implements CompanyClient {

    @Override
    public ApiResponse<?> fetchCompanyById(@PathVariable String id){
        return new ApiResponse<>(
                false,
                "Company data not reachable",
                null,
                null
        );
    }

    @Override
    public ApiResponse<?> fetchAllCompanies() {
        return new ApiResponse<>(
                false,
                "Company data not reachable",
                null,
                null
                );
    }

    @Override
    public ApiResponse<?> searchCompanyByName(@RequestParam(value = "companyName", required = true) String companyName) {
        return new ApiResponse<>(
            false,
            "Company search service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> searchUsersByUsername(String assignee) {
            return new ApiResponse<>( false, "Company search service is currently unavailable", null, null);
    }

    @Override
    public ApiResponse<?> fetchAllActiveUsers(String role) {
        return new ApiResponse<>(
            false,
            "Active users data not reachable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> fetchUserById(String userId) {
        return new ApiResponse<>(
            false,
            "User data not reachable",
            null,
            null
        );
    }
}
