package bw.org.hrdc.weblogic.workplacelearning.dto;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * DTO for managing Acknowledgement details.
 */
@Data
@Schema(
        name = "Acknowledgement",
        description = "Schema to hold Acknowledgement details"
)
public class AcknowledgementDto {

    @Schema(description = "Unique identifier for the acknowledgement", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @NotNull(message = "Reference number cannot be null")
    @Schema(description = "Reference number for the acknowledgement", example = "ACK-2024-0001")
    private String referenceNo;

    @NotNull(message = "Date generated cannot be null")
    @Schema(description = "Date the acknowledgement was generated", example = "2024-11-26T10:00:00Z")
    private Date dateGenerated;

    @Schema(description = "Status of the acknowledgement", example = "APPROVED")
    private ApplicationStatus acknowledgementStatus;

    @NotNull(message = "Non-Credit Bearing Course ID cannot be null")
    @Schema(description = "The ID of the associated Non-Credit Bearing Course", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID nonCreditBearingCourseId;
}
