package bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.AssessmentCriteriaDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.LearningOutcomeDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.CourseContentDelivery;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.LearningOutcome;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class LearningOutcomeMapper {

    public static LearningOutcome toEntity(LearningOutcomeDto model, CourseContentDelivery courseContentDelivery) {
        if (model == null) {
            return null;
        }
        LearningOutcome learningOutcome =  new LearningOutcome();
        learningOutcome.setOutcome(model.getOutcome());
        learningOutcome.setCourseContentDelivery(courseContentDelivery);
        return learningOutcome;
    }

    public static LearningOutcomeDto toDto(LearningOutcome entity) {
        if (entity == null) {
            return null;
        }
        LearningOutcomeDto learningOutcome =  new LearningOutcomeDto();
        learningOutcome.setOutcome(entity.getOutcome());

        Set<AssessmentCriteriaDto> assessmentCriteria = entity.getAssessmentCriteria() != null
                ? AssessmentCriteriaMapper.toDtoList(entity.getAssessmentCriteria())
                : Collections.emptySet();

        learningOutcome.setAssessmentCriteria(assessmentCriteria);
        return learningOutcome;
    }

    public static Set<LearningOutcomeDto> toDtoList(Set<LearningOutcome> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(LearningOutcomeMapper::toDto)
                .collect(Collectors.toSet());
    }
}
