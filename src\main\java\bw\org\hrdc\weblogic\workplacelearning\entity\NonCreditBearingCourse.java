package bw.org.hrdc.weblogic.workplacelearning.entity;


import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

/**
 * Created by <PERSON><PERSON><PERSON> on 26 November 2024
 */

@Entity
@Table(name = "non_credit_bearing_course")
@EntityListeners(AuditingEntityListener.class)
@Getter
@Setter
@ToString
public class NonCreditBearingCourse {

    @Id
    @GeneratedValue
    private UUID id;

    @Column(nullable = false)
    private String title;

    @Column(nullable = false)
    private String type;

    @Column(name = "field_of_learning", nullable = false)
    private String fieldOfLearning;

    @Column(name = "learning_time_in_hours", nullable = false)
    private int learningTimeInHours;

    @Column(name = "date_developed", nullable = false)
    private Date dateDeveloped;

    @Column(name = "target_population", nullable = false)
    private String targetPopulation;

    @Column(name = "entry_requirements", nullable = false)
    private String entryRequirements;

    @Column(name = "organisation_id", nullable = false)
    private UUID organisationId;

    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createdAt;

    @CreatedBy
    @Column(updatable = false)
    private String createdBy;

    @LastModifiedDate
    @Column(insertable = false)
    private LocalDateTime updatedAt;

    @LastModifiedBy
    @Column(insertable = false)
    private String updatedBy;
}

