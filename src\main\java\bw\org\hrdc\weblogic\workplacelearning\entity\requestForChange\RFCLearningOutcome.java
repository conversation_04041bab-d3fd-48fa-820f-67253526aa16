package bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.util.UUID;
@Entity
@Table(name = "rfc_learning_outcomes")
@EntityListeners(AuditingEntityListener.class)
@Getter
@Setter
@ToString
public class RFCLearningOutcome extends Base {

    @Column(nullable = false)
    private String topic;

    @Column(nullable = false)
    private String objective;

    @Column(nullable = false)
    private String deliveryStrategy;

    @Column(nullable = false)
    private String assessmentStrategy;

    @Column(nullable = false)
    private String outcome;

    @ManyToOne
    @JoinColumn(name = "application_id", nullable = false)
    @JsonIgnore
    @ToString.Exclude
    private RequestForSignificantChanges application;
}

