package bw.org.hrdc.weblogic.workplacelearning.entity;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.UUID;

@Entity
@Table(name = "training_detail")
@Getter
@Setter
@ToString
public class TrainingDetail {

    @Id
    @GeneratedValue
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "workplace_training_plan_id", nullable = false)
    private WorkPlaceTrainingPlan workPlaceTrainingPlan;

    @Column(name = "learning_programme", nullable = false)
    private String learningProgramme;

    @Column(name = "skills_to_be_acquired", nullable = false)
    private String skillsToBeAcquired;

    @Temporal(TemporalType.DATE)
    @Column(name = "training_dates", nullable = false)
    private Date trainingDates;

    @Column(name = "institution_name", nullable = false)
    private String institutionName;

    @Column(name = "location", nullable = false)
    private String location;

    @Column(name = "accrediting_body", nullable = false)
    private String accreditingBody;

    @Column(name = "level_of_training", nullable = false)
    private String levelOfTraining;

    @Column(name = "cost_of_training", nullable = false)
    private Double costOfTraining;

    @Column(name = "number_of_participants", nullable = false)
    private Integer numberOfParticipants;

    @Column(name = "citizen_status", nullable = false)
    private String citizenStatus; // e.g., "Citizen" or "Non-Citizen"

    @Column(name = "total_people_to_be_trained", nullable = false)
    private Integer totalPeopleToBeTrained;
}
