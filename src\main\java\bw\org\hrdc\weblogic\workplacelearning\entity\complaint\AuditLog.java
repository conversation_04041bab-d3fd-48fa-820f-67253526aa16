package bw.org.hrdc.weblogic.workplacelearning.entity.complaint;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Auditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @CreatedOn 27/03/25 22:01
 * @UpdatedBy martinspectre
 * @UpdatedOn 27/03/25 22:01
 */
@Entity
@Table(name = "audit_logs")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AuditLog extends Auditable implements Serializable {

    @ManyToOne
    @JoinColumn(name = "complaint_id", nullable = false)
    @JsonIgnore
    private ComplaintEntity complaint;

    private String action;
    private String description;
    private String performedBy;

    @CreationTimestamp
    private LocalDateTime timestamp;
}
