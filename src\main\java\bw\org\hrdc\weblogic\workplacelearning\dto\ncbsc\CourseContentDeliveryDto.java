package bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @CreatedOn 18/02/25 20:05
 * @UpdatedBy martinspectre
 * @UpdatedOn 18/02/25 20:05
 */
@Data @AllArgsConstructor @NoArgsConstructor
public class CourseContentDeliveryDto{
    private String exitLevelOutcomes;
    private String learningOutcomesSummary;
    private String shortCourseDeliveryMode;
    private String shortCourseDeliveryType;
    private String location;
    private Set<LearningOutcomeDto> learningOutcomes;
}
