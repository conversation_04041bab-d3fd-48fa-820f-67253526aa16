package bw.org.hrdc.weblogic.workplacelearning.repository;

import bw.org.hrdc.weblogic.workplacelearning.entity.ETPEvaluation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * Repository for ETPEvaluation entity.
 */
@Repository
public interface ETPEvaluationRepository
        extends JpaRepository<ETPEvaluation, UUID>, JpaSpecificationExecutor<ETPEvaluation> {

    /**
     * Find evaluations by their organisation ID.
     *
     * @param organisationId the organisation ID.
     * @return a list of evaluations belonging to the given organisation.
     */
    List<ETPEvaluation> findByOrganisationId(UUID organisationId);

    /**
     * Find evaluations by user ID.
     *
     * @param userId the user ID.
     * @return a list of evaluations created by the given user.
     */
    List<ETPEvaluation> findByUserId(UUID userId);

    /**
     * Find evaluations by meeting date.
     *
     * @param meetingDate the meeting date.
     * @return a list of evaluations conducted on the given date.
     */
    List<ETPEvaluation> findByMeetingDate(java.util.Date meetingDate);
}
