package bw.org.hrdc.weblogic.workplacelearning.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "particular_of_training")
@Getter
@Setter
@ToString
public class ParticularOfTraining {

    @Id
    @GeneratedValue
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "pre_approval_application_id", nullable = false)
    @JsonIgnoreProperties({"particularOfTrainings", "moduleDetails", "estimatedTrainingCosts", "comments"})
    private PreApprovalApplication preApprovalApplication;

    @Column(name = "product_related_training")
    private Boolean productRelatedTraining;

    @Column(name = "outsourced_training")
    private Boolean outsourcedTraining;

    @Column(name = "non_citizen_employee")
    private Boolean nonCitizenEmployee;

    @Column(name = "is_training_local")
    private Boolean isTrainingLocal;
}
