package bw.org.hrdc.weblogic.workplacelearning.api;

import bw.org.hrdc.weblogic.workplacelearning.common.config.FeignConfig;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "ACCOUNT-SERVICE", fallback = CompanyClientFallback.class, configuration = FeignConfig.class, contextId = "companyClient")
public interface CompanyClient {

    @GetMapping("/api/v1/company/{id}")
    ApiResponse<?> fetchCompanyById(@PathVariable String id);

    @GetMapping("/api/v1/company/all")
    ApiResponse<?> fetchAllCompanies();

    @GetMapping("/api/v1/company/search")
    ApiResponse<?> searchCompanyByName(@RequestParam(value = "companyName", required = true) String companyName);

    @GetMapping("/api/v1/user/backoffice/search")
    ApiResponse<?> searchUsersByUsername(@RequestParam(name = "assignee") String assignee);

    @GetMapping("/api/v1/user/backoffice/users-by-role/{role}")
    ApiResponse<?> fetchAllActiveUsers(@PathVariable("role") String role);

    @GetMapping("/api/v1/user/backoffice/user/{userId}")
    ApiResponse<?> fetchUserById(@PathVariable("userId") String userId);
}
