package bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "short_course_information",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"uuid", "application_id"}),
                @UniqueConstraint(columnNames = {"uuid"})
        })
@Setter @Getter
public class ShortCourseInformation extends Base implements Serializable {

    @Column(name = "title", nullable = false)
    private String title;

    @Column(name = "type", nullable = false)
    private String type;

    @Column(name = "duration")
    private String duration;

    @Column(name = "field_of_learning", nullable = false)
    private String fieldOfLearning;

    @Column(name = "sub_field_of_learning")
    private String subFieldOfLearning;

    @Column(name = "level")
    private String level;

    @Column(name = "accrediting_body")
    private String accreditingBody;

    @Column(name = "course_learning_time", nullable = false)
    private int courseLearningTime;

    @Column(name = "start_date")
    private Date startDate;

    @Column(name = "end_date")
    private Date endDate;

    @Column(name = "year_due_for_review", nullable = false)
    private String yearDueForReview;

    @Column(name = "target_population", nullable = false)
    private String targetPopulation;

    @Column(name = "entry_requirements", nullable = false)
    private String entryRequirements;

    @OneToOne
    @JoinColumn(name = "application_id", nullable = false)
    @JsonBackReference
    @ToString.Exclude
    private NCBSCApplication application;
}
