package bw.org.hrdc.weblogic.workplacelearning.service;

import bw.org.hrdc.weblogic.workplacelearning.dto.ETPEvaluationDto;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.UUID;

/**
 * Service interface for ETPEvaluation.
 */
public interface IETPEvaluationService {

    /**
     * Creates a new ETP Monitoring & Evaluation.
     *
     * @param evaluationDto the DTO containing evaluation details.
     */
    void createEvaluation(ETPEvaluationDto evaluationDto);

    /**
     * Updates an existing ETP Monitoring & Evaluation.
     *
     * @param evaluationDto the DTO containing updated evaluation details.
     */
    void updateEvaluation(ETPEvaluationDto evaluationDto);

    /**
     * Retrieves an ETP Monitoring & Evaluation by its ID.
     *
     * @param id the unique identifier of the evaluation.
     * @return the DTO containing evaluation details.
     */
    ETPEvaluationDto getEvaluation(UUID id);

    /**
     * Fetches all evaluations based on search criteria and pagination.
     *
     * @param meetingDate    the date of the evaluation meeting.
     * @param userId         the ID of the user who conducted the evaluation.
     * @param organisationId the organisation associated with the evaluation.
     * @param pageable       the pagination and sorting information.
     * @return a paginated list of evaluations matching the criteria.
     */
    Page<ETPEvaluationDto> fetchAllEvaluations(
            Date meetingDate, UUID userId, UUID organisationId, Pageable pageable);

    /**
     * Deletes an ETP Monitoring & Evaluation by its ID.
     *
     * @param id the unique identifier of the evaluation to delete.
     */
    @Transactional
    void deleteEvaluation(UUID id);
}
