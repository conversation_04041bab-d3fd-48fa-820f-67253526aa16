package bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc.noc;

import bw.org.hrdc.weblogic.workplacelearning.dto.document.FileDocumentDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.NOCDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Component
public class NOCDtoApplicationMapper {

    public static NOCApplication toEntity(NOCDto dto) {
        if(dto ==null){
            return null;
        }

        NOCApplication entity = new NOCApplication();

        entity.setReferenceNumber(dto.getReferenceNumber());
        entity.setApplicationNumber(dto.getApplicationNumber());
        entity.setRecognitionNumber(dto.getRecognitionNumber());
        entity.setOrganisationId(dto.getOrganisationId());
        entity.setTrainingNeedsAssessmentPurpose(dto.getTrainingNeedsAssessmentPurpose());
        entity.setTrainingNeedsAssessmentSkillsNeedsAnalysis(dto.getTrainingNeedsAssessmentSkillsNeedsAnalysis());
        entity.setShortCourseDeliveryMode(dto.getShortCourseDeliveryMode());
        entity.setKeyFacilitation(dto.getKeyFacilitation());
        entity.setAssessmentType(dto.getAssessmentType());
        entity.setCertification(dto.getCertification());
        entity.setThirdPartyArrangements(dto.getThirdPartyArrangements());
        entity.setResources(dto.getResources());
        entity.setShortCourseEndorsement(dto.getShortCourseEndorsement());
        entity.setJustification(dto.getJustification());
        entity.setDateSubmitted(dto.getDateSubmitted() != null ? dto.getDateSubmitted() : LocalDateTime.now());
        entity.setApplicationStatus(dto.getApplicationStatus());
        entity.setApplicationState(dto.getApplicationState());
        entity.setSeverity(dto.getSeverity());
        entity.setMerged(dto.isMerged());
        entity.setIsMajorChange(dto.getIsMajorChange());
        entity.setShortCourseInformation(dto.getShortCourseInformation());
        entity.setCourseContentAndDelivery(dto.getCourseContentAndDelivery());
        entity.setScopeOfAccreditation(dto.getScopeOfAccreditation());
        entity.setProcessInstanceId(dto.getProcessInstanceId());

        return entity;
    }

    public static NOCDto toDto(NOCApplication entity, List<FileDocumentDto> attachments) {
        if(entity == null){
            return null;
        }

        NOCDto dto = new NOCDto();

        dto.setReferenceNumber(entity.getReferenceNumber());
        dto.setApplicationNumber(entity.getApplicationNumber());
        dto.setRecognitionNumber(entity.getRecognitionNumber());
        dto.setOrganisationId(entity.getOrganisationId());
        dto.setTrainingNeedsAssessmentPurpose(entity.getTrainingNeedsAssessmentPurpose());
        dto.setTrainingNeedsAssessmentSkillsNeedsAnalysis(entity.getTrainingNeedsAssessmentSkillsNeedsAnalysis());
        dto.setShortCourseDeliveryMode(entity.getShortCourseDeliveryMode());
        dto.setKeyFacilitation(entity.getKeyFacilitation());
        dto.setAssessmentType(entity.getAssessmentType());
        dto.setCertification(entity.getCertification());
        dto.setThirdPartyArrangements(entity.getThirdPartyArrangements());
        dto.setResources(entity.getResources());
        dto.setShortCourseEndorsement(entity.getShortCourseEndorsement());
        dto.setJustification(entity.getJustification());
        dto.setDateSubmitted(entity.getDateSubmitted());
        dto.setApplicationStatus(entity.getApplicationStatus());
        dto.setApplicationState(entity.getApplicationState());
        dto.setSeverity(entity.getSeverity());
        dto.setMerged(entity.isMerged());
        dto.setIsMajorChange(entity.getIsMajorChange());
        dto.setShortCourseInformation(entity.getShortCourseInformation());
        dto.setCourseContentAndDelivery(entity.getCourseContentAndDelivery());
        dto.setScopeOfAccreditation(entity.getScopeOfAccreditation());
        dto.setProcessInstanceId(entity.getProcessInstanceId());
        dto.setAttachments(attachments == null ? Collections.emptyList() : attachments);

        return dto;
    }
}
