package bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining;

import bw.org.hrdc.weblogic.workplacelearning.dto.document.FileDocumentDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

/**
 * DTO for WorkPlaceTrainingPlan response.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(
        name = "WorkPlaceTrainingPlanResponse",
        description = "Schema to hold Workplace Training Plan response details"
)
public class WorkPlaceTrainingPlanResponseDto {

    @Schema(description = "Status code of the response", example = "201")
    private String statusCode;

    @Schema(description = "Status message of the response", example = "Workplace Training Plan created successfully.")
    private String statusMsg;

    @Schema(description = "ID of the created/updated training plan", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID applicationId;

    @Schema(description = "State of the application", example = "DRAFT")
    private String state;

    @Schema(description = "Status of the application", example = "INITIAL")
    private String status;

    @Schema(description = "Application number", example = "WTP-20240408-ABC123")
    private String applicationNumber;

    @Schema(description = "Reference number", example = "REF-20240408-XYZ789")
    private String referenceNumber;

    @Schema(description = "process Instance id for the application", example = "4089654455456465")
    private String processInstanceId;

    private List<FileDocumentDto> attachments;

}
