package bw.org.hrdc.weblogic.workplacelearning.entity.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * eHRDF Base Extended Object representation
 * <AUTHOR>
 *
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass @JsonIgnoreProperties(ignoreUnknown=true)
@EntityListeners(AuditingEntityListener.class)
@Data
public abstract class Auditable extends Base {

    @CreationTimestamp
	@CreatedDate
	@Column(name = "created_at", nullable = false, updatable = false)
	@JsonProperty(value="CreatedAt")
	private LocalDateTime createdAt;

    @UpdateTimestamp
	@Column(name = "updated_at", nullable = false)
	@JsonProperty(value="UpdatedAt")
	@LastModifiedDate
	private LocalDateTime updatedAt;

	@Column(name = "created_by", nullable = false, updatable = false)
	@JsonProperty(value="CreatedBy")
	@CreatedBy
	private String createdBy;

	@Column(name = "updated_by", nullable = false)
	@JsonProperty(value="UpdatedBy")
	@LastModifiedBy
	private String updatedBy;

	@Column(name = "deleted", nullable = false)
	@JsonProperty(value="Deleted")
	private boolean isDeleted = false;
}
