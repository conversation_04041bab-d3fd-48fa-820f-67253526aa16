package bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.noc;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @CreatedOn 14/04/25 10:22
 * @UpdatedBy martinspectre
 * @UpdatedOn 14/04/25 10:22
 */
public interface NCBSCChangeRequestRepository extends JpaRepository<NOCApplication, UUID>, JpaSpecificationExecutor<NOCApplication> {
    Optional<NOCApplication> findByApplicationNumber(String applicationNumber);

    Page<NOCApplication> findByOrganisationId(String organisationId, Pageable pageable);

    Page<NOCApplication> findByApplicationStatus(Enums.Status status, Pageable pageable);

    Page<NOCApplication> findByApplicationState(Enums.State state, Pageable pageable);

    Page<NOCApplication> findBySeverity(Enums.ChangeSeverity severity, Pageable pageable);
}
