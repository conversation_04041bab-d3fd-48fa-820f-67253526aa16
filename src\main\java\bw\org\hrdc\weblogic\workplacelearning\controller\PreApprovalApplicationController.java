package bw.org.hrdc.weblogic.workplacelearning.controller;
import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.*;
import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.ApplicationStatusUpdatePayload;
import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplicationComments;
import bw.org.hrdc.weblogic.workplacelearning.exception.ResourceNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.mapper.PreApprovalApplicationMapper;
import bw.org.hrdc.weblogic.workplacelearning.service.PreApprovalApplicationCommentsService;
import bw.org.hrdc.weblogic.workplacelearning.service.IPreApprovalApplicationService;
import bw.org.hrdc.weblogic.workplacelearning.service.NotificationService;
import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationConstants;
import bw.org.hrdc.weblogic.workplacelearning.service.document.FileDocumentService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.util.ReferenceNumberGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.modelmapper.ModelMapper;


import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.ArrayList;
import java.util.stream.Collectors;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import java.util.HashSet;
import java.util.Set;
import java.util.Collections;
import java.util.Arrays;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums.UserRoles;
import bw.org.hrdc.weblogic.workplacelearning.entity.EstimatedTrainingCosts;
import java.util.Objects;
// import bw.org.hrdc.weblogic.workplacelearning.models.company.*;

/**
 * REST Controller for managing Pre-Approval Applications.
 */
@Slf4j
@RestController
@RequestMapping(path = "/api/v1/pre-approval-applications", produces = {MediaType.APPLICATION_JSON_VALUE})
@Validated
@Tag(
        name = "REST APIs for Pre-Approval Application Management",
        description = "REST APIs to CREATE, UPDATE, FETCH and DELETE Pre-Approval Applications"
)
public class PreApprovalApplicationController {
    private static final Logger logger = LoggerFactory.getLogger(PreApprovalApplicationController.class);

    private final IPreApprovalApplicationService applicationService;
    private final PreApprovalApplicationCommentsService commentsService;
    private final CompanyClient companyClient;
    private final WorkflowClient workflowClient;
    private final NotificationService notificationService;

    @Autowired
    private FileDocumentService fileDocumentService;

    public PreApprovalApplicationController(IPreApprovalApplicationService applicationService,
                                          PreApprovalApplicationCommentsService commentsService,
                                          CompanyClient companyClient,
                                          WorkflowClient workflowClient,
                                          NotificationService notificationService) {
        this.applicationService = applicationService;
        this.commentsService = commentsService;
        this.companyClient = companyClient;
        this.workflowClient = workflowClient;
        this.notificationService = notificationService;
    }

    @Operation(
            summary = "Create, Save Draft, or Update Pre-Approval Application REST API",
            description = "REST API to create, save as draft, or update a Pre-Approval Application. " +
                         "If an ID is provided, it will update the application. " +
                         "If no ID is provided and isDraft=true, it will create a draft application. " +
                         "If no ID is provided and isDraft=false, it will create a submitted application."
    )
    @PostMapping("/create")
    public ResponseEntity<PreApprovalApplicationResponseDto> createOrUpdatePreApprovalApplication(
            @Valid @RequestBody PreApprovalApplicationDto applicationDto,
            @RequestParam(defaultValue = "false") boolean isDraft) {
        try {
            log.info("Processing create/update request for pre-approval application, isDraft: {}", isDraft);
            PreApprovalApplicationResponseDto response;

            if (applicationDto.getId() != null) {
                log.info("Updating existing application with ID: {}, isDraft: {}", applicationDto.getId(), isDraft);

                    PreApprovalApplication existingApplication = applicationService.getPreApprovalApplicationEntity(applicationDto.getId())
                            .orElseThrow(() -> new ResourceNotFoundException("Application not found"));

                if (isDraft) {
                    log.info("Updating as draft application");
                    applicationDto.setState(Enums.State.DRAFT.name());
                    applicationDto.setStatus(Enums.Status.INITIAL.name());
                } else {
                    log.info("Updating as submitted application");

                    applicationDto.setStatus(Enums.Status.PENDING.name());
                    if (Enums.State.DRAFT.name().equals(existingApplication.getState()) &&
                        (existingApplication.getReferenceNumber() == null || existingApplication.getReferenceNumber().isEmpty())) {
                        log.info("Converting draft to submitted application - generating reference number");
                        String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("REF");
                        applicationDto.setState(Enums.State.SUBMITTED.name());
                        applicationDto.setReferenceNumber(referenceNumber);
                        log.info("Generated reference number: {} for application ID: {}", referenceNumber, applicationDto.getId());
                    }
                }

                // Use the mapper to update the existing entity with the DTO data
                PreApprovalApplicationMapper mapper = new PreApprovalApplicationMapper(new ModelMapper());
                mapper.updateEntityFromDto(applicationDto, existingApplication);

                applicationService.updatePreApprovalApplication(applicationDto);

                PreApprovalApplication updatedApplication = applicationService.getPreApprovalApplicationEntity(applicationDto.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Application not found after update"));

                if(applicationDto.getAttachments() != null && updatedApplication != null){
                    logger.info("Saved Application get Uuid {}", updatedApplication.getId());
                    fileDocumentService.saveDocuments(String.valueOf(updatedApplication.getId()), Enums.FileEntityType.PRE_APPROVAL_APPLICATION, applicationDto.getAttachments());
                    logger.info("Saved Attachements for id {}", updatedApplication.getId());
                }

                response = PreApprovalApplicationResponseDto.builder()
                    .statusCode(ApplicationConstants.STATUS_200)
                    .statusMsg("Pre-Approval Application updated successfully.")
                    .applicationId(applicationDto.getId())
                    .state(updatedApplication.getState())
                    .status(updatedApplication.getStatus())
                    .applicationNumber(updatedApplication.getApplicationNumber())
                    .referenceNumber(updatedApplication.getReferenceNumber())
                    .vatNumber(updatedApplication.getVatNumber())
                    .attachments(applicationDto.getAttachments())
                    .build();

                log.info("Application updated successfully with ID: {}", applicationDto.getId());
            } else {
                if (isDraft) {
                    log.info("Saving draft pre-approval application");

                    applicationDto.setState(Enums.State.DRAFT.name());
                    applicationDto.setStatus(Enums.Status.INITIAL.name());

                    // Generate application number for draft
                    String applicationNumber = ReferenceNumberGenerator.generateReferenceNumber("APP");
                    applicationDto.setApplicationNumber(applicationNumber);

                    response = applicationService.createPreApprovalApplication(applicationDto);

                    // Set application number in response
                    response.setApplicationNumber(applicationNumber);

                    log.info("Draft pre-approval application saved successfully with ID: {}", response.getApplicationId());
                }
                else {
                    log.info("Creating new application");

                    applicationDto.setState(Enums.State.SUBMITTED.name());
                    applicationDto.setStatus(Enums.Status.PENDING.name());

                    // Generate both application number and reference number for submitted application
                    String applicationNumber = ReferenceNumberGenerator.generateReferenceNumber("APP");
                    String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("REF");

                    applicationDto.setApplicationNumber(applicationNumber);
                    applicationDto.setReferenceNumber(referenceNumber);

                    response = applicationService.createPreApprovalApplication(applicationDto);

                    // Set both application number and reference number in response
                    response.setApplicationNumber(applicationNumber);
                    response.setReferenceNumber(referenceNumber);

                    log.info("New application created successfully with ID: {}", response.getApplicationId());

                    try {
                        Map<String, Object> workflowResponse = workflowClient.getNCBSCApplication(Enums.ApplicationType.PRE_APPROVAL.name(), response.getApplicationId().toString());
                        
                        log.info("Workflow response: {}", workflowResponse);
                        if (workflowResponse != null && Boolean.TRUE.equals(workflowResponse.get("success"))) {
                            Object data = workflowResponse.get("applicationData");
                            if (data != null) {
                                String processInstanceId = (String) ((Map<String, Object>)workflowResponse).get("processInstanceId");
                                // Save the processInstanceId to the database
                                String preApprovalId = response.getApplicationId().toString();
                                applicationService.updateProcessInstanceIdToPreApprovalApplication(preApprovalId, processInstanceId);
                                response.setProcessInstanceId(processInstanceId);
                                log.info("Process instance ID {} saved for application {}", processInstanceId, preApprovalId);
                            }
                        }
                    } catch (Exception e) {
                        log.error("Failed to start workflow process: {}", e.getMessage());
                        // Continue execution even if workflow process fails
                    }

                }
            }

            // saving for drafts
            PreApprovalApplication draftApplication = applicationService.fetchApplicationByReferenceNumber(applicationDto.getReferenceNumber());
            if(applicationDto.getAttachments() != null && draftApplication != null){
                logger.info("Saved Application get Uuid {}", draftApplication.getId());
                response.setAttachments(fileDocumentService.saveDocuments(String.valueOf(draftApplication.getId()), Enums.FileEntityType.PRE_APPROVAL_APPLICATION, applicationDto.getAttachments()));
                logger.info("Saved Attachments for id {}", draftApplication.getId());
            }

            return ResponseEntity
                    .status(HttpStatus.CREATED)
                    .body(response);
        } catch (ResourceNotFoundException e) {
            log.error("Resource not found: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error processing create/update request: {}", e.getMessage());
            throw e;
        }
    }

    @Operation(
            summary = "Soft Delete Pre-Approval Application REST API",
            description = "REST API to soft delete a Pre-Approval Application"
    )
    @DeleteMapping("/soft-delete/{id}")
    public ResponseEntity<ResponseDto> softDeletePreApprovalApplication(@PathVariable UUID id) {
        applicationService.softDeletePreApprovalApplication(id);
        return ResponseEntity.ok(
                new ResponseDto(ApplicationConstants.STATUS_200, "Pre-Approval Application soft deleted successfully."));
    }

    @Operation(
            summary = "Assign User to Pre-Approval Application REST API",
            description = "REST API to assign a user to a Pre-Approval Application"
    )
    @PutMapping("/{applicationId}/assign-user")
    public ResponseEntity<ResponseDto> assignAgent(@PathVariable UUID applicationId, @RequestBody Map<String, Object> payload) {
        log.info("Application user assignment initiated for application ID: {}", applicationId);
        try {
            String role = payload.get("role").toString();
            String userId = payload.get("userId").toString();

            if(role.isEmpty() || userId.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ResponseDto("MANDATORY_FIELDS_MISSING",
                        "Required fields are missing, please provide user id and their respective role"));
            }

            Optional<PreApprovalApplication> application = applicationService.getPreApprovalApplicationEntity(applicationId);

            if (application.isPresent()) {
                int updatedResult = applicationService.updateApplicationAssignedUser(applicationId, Enums.UserRoles.valueOf(role), userId);
                if (updatedResult == 0) {
                    log.error("Failed to update application ID: {}", applicationId);
                    return ResponseEntity.badRequest()
                        .body(new ResponseDto("APPLICATION_ERROR", "Failed to process application."));
                } else {
                    //TODO trigger notification to agent for the assignment made

                     if (Enums.UserRoles.AGENT.name().equalsIgnoreCase(role)) {
                            notificationService.sendNotificationToUser(
                                    userId,
                                    application.get().getReferenceNumber(),
                                    application.get().getId().toString(),
                                    application.get().getStatus(),
                                    Enums.NotificationType.IN_APP.name(),
                                    Enums.ApplicationType.PRE_APPROVAL.name()
                                    
                            );
                    } else if (Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role)) {
                            notificationService.sendNotificationToUser(
                                    userId,
                                    application.get().getReferenceNumber(),
                                    application.get().getId().toString(),
                                    application.get().getStatus(),
                                    Enums.NotificationType.IN_APP.name(),
                                    Enums.ApplicationType.PRE_APPROVAL.name()
                            );
                    } 
                    return ResponseEntity.ok(new ResponseDto("SUCCESS", "Application assigned successfully"));
                }
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ResponseDto("APPLICATION_NOT_FOUND",
                        "Provided application identifier does not exist"));
            }
        } catch (Exception e) {
            log.error("Failed to assign application ID {} with exception: {}", applicationId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ResponseDto("INTERNAL_SERVER_ERROR", e.getMessage()));
        }
    }


    @Operation(
            summary = "Update Application Status REST API",
            description = "REST API to update the status of a Pre-Approval Application"
    )
    @PutMapping("/{applicationId}/status-update")
    public ResponseEntity<ResponseDto> changeApplicationStatus(
            @PathVariable UUID applicationId,
            @RequestBody ApplicationStatusUpdatePayload payload) {
        log.info("Application status update initiated for application ID: {}", applicationId);
        try {
            String role = payload.getRole();
            String userId = payload.getUserId();
            String action = payload.getAction();
            String comments = payload.getComments();
            String newAssignee = payload.getNewAssignee();
            String actionType = null;

            if(Enums.UserRoles.AGENT.name().equalsIgnoreCase(role)) {
                actionType = "Agent_action";
            }else if(Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role)) {
                actionType = "Officer_action";
            }else if(Enums.UserRoles.MANAGER.name().equalsIgnoreCase(role)) {
                actionType = "Manager_action";
            }

            if(action.isEmpty() || userId.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ResponseDto("MANDATORY_FIELDS_MISSING",
                        "Required fields are missing, please provide user id and action"));
            }

            Optional<PreApprovalApplication> application = applicationService.getPreApprovalApplicationEntity(applicationId);
            if (application.isPresent()) {
                int updatedResult = applicationService.updateApplicationStatus(applicationId, role, action, newAssignee);
                if (updatedResult == 0) {
                    log.error("Failed to update status of application ID: {}", applicationId);
                    return ResponseEntity.badRequest()
                        .body(new ResponseDto("APPLICATION_ERROR", "Failed to process application."));
                } else {
                    // Create and save comments if provided
                    if (comments != null && !comments.isEmpty()) {
                        PreApprovalApplicationComments logEntry = new PreApprovalApplicationComments();
                        logEntry.setApplication(application.get());
                        logEntry.setAction(action);
                        logEntry.setComments(applicationService.sanitizeHtml(comments));
                        logEntry.setUpdatedBy(userId);
                        logEntry.setTimestamp(LocalDateTime.now());
                        commentsService.createComments(logEntry);
                    }

                    String processInstanceId = application.get().getProcessInstanceId();
                    String preApprovalId = application.get().getId().toString();

                    if (processInstanceId != null && !processInstanceId.isEmpty()) {
                        try {
                            Map<String, Object> workflowPayload = new HashMap<>();
                            workflowPayload.put("referenceNumber", preApprovalId);
                            workflowPayload.put("ApplicationType", Enums.ApplicationType.PRE_APPROVAL.name());
                            workflowPayload.put("role", role);
                            logger.info("User Role : {} and ref number :{}", role, preApprovalId);
                            workflowClient.resumeProcess(processInstanceId, actionType, workflowPayload);
                        } catch (Exception e) {
                            // Log the error but continue with the application status update
                            logger.error("Failed to resume workflow process: {}", e.getMessage());
                            // Don't rethrow the exception - allow the application status update to succeed
                        }
                    } else {
                        logger.warn("No process instance ID found for application {}", preApprovalId);
                    }

                    //TODO trigger notification to agent for the status update
                    return ResponseEntity.ok(new ResponseDto("SUCCESS", "Application status updated successfully"));
                }
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ResponseDto("APPLICATION_NOT_FOUND",
                        "Provided application identifier does not exist"));
            }
        } catch (Exception e) {
            log.error("Failed to update status of application ID: {} with exception: {}", applicationId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ResponseDto("INTERNAL_SERVER_ERROR", e.getMessage()));
        }
    }

    @PostMapping("/batch-status-update")
    public ResponseEntity<?> batchChangeApplicationStatus(@RequestBody BatchStatusUpdateRequest payload) {
        log.info("Batch application status update initiated for {} applications", 
                payload.getApplicationIds() != null ? payload.getApplicationIds().size() : 0);
        
        try {
            String role = payload.getRole();
            String userId = payload.getUserId();
            String action = payload.getAction();
            String comments = payload.getComments();
            String newAssignee = payload.getNewAssignee();
            List<UUID> applicationIds = payload.getApplicationIds();

            if (action.isEmpty() || userId.isEmpty() || applicationIds == null || applicationIds.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ResponseDto("MANDATORY_FIELDS_MISSING", 
                        "Required fields are missing, please provide user id, action and application references"));
            }

            BatchStatusUpdateResult result = applicationService.batchUpdateApplicationStatus(
                    applicationIds, role, action, userId, comments, newAssignee);
            
            return ResponseEntity.ok(new ApiResponse<>(true, 
                    "Batch status update processed: " + result.getSuccessCount() + " successful, " + 
                    result.getFailureCount() + " failed", result, null));
            
        } catch (Exception e) {
            log.error("Failed to process batch status update with exception: {}", e.getMessage());
            return ApiResponse.getInternalServerError(e.getMessage());
        }
    }

    // for client table view
    @Operation(
            summary = "Get Company Pre-Approval Applications REST API",
            description = "REST API to fetch Pre-Approval Applications for a specific company"
    )
    @GetMapping("/company/{companyId}/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAllCompanyApplications(
            @PathVariable UUID companyId,
            @PathVariable Integer offset,
            @PathVariable Integer pageSize) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Page<PreApprovalApplicationListDto> applications = applicationService.getAllCompanyApplications(
                    companyId, PageRequest.of(offset, pageSize));

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, e.getMessage(), null, null));
        }
    }

  // for client view and edit
    @Operation(
            summary = "Get Pre-Approval Application by ID REST API",
            description = "REST API to get a Pre-Approval Application by ID with all related details including company, employees, training details, costs, and comments"
    )
    @GetMapping("/application-number/{applicationId}")
    public ResponseEntity<ApiResponse<?>> getApplicationById(@PathVariable String applicationId) {
        try {
            PreApprovalApplicationDto application = applicationService.getApplicationByApplicationNumber(applicationId);
            return buildApplicationResponse(application, applicationId);
        } catch (ResourceNotFoundException e) {
            log.error("Application not found with application number {}: {}", applicationId, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>(false, e.getMessage(), null, null));
        } catch (Exception e) {
            log.error("Failed to fetch application with application number {} with exception: {}", applicationId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, e.getMessage(), null, null));
        }
    }
    
    @Operation(
            summary = "Get Pre-Approval Application by Reference Number REST API",
            description = "REST API to get a Pre-Approval Application by Reference Number with all related details including company, employees, training details, costs, and comments"
    )
    @GetMapping("/reference-number/{referenceNumber}")
    public ResponseEntity<ApiResponse<?>> getApplicationByReferenceNumber(@PathVariable String referenceNumber) {
        try {
            PreApprovalApplicationDto application = applicationService.getApplicationByReferenceNumber(referenceNumber);
            return buildApplicationResponse(application, application.getId().toString());
        } catch (ResourceNotFoundException e) {
            log.error("Application not found with reference number {}: {}", referenceNumber, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>(false, e.getMessage(), null, null));
        } catch (Exception e) {
            log.error("Failed to fetch application with reference number {} with exception: {}", referenceNumber, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, e.getMessage(), null, null));
        }
    }
    
    /**
     * Helper method to build a consistent response for application endpoints
     * 
     * @param application The application DTO
     * @param applicationId The application ID as a string
     * @return ResponseEntity with ApiResponse
     */
    private ResponseEntity<ApiResponse<?>> buildApplicationResponse(PreApprovalApplicationDto application, String applicationId) {
        Map<String, Object> response = new HashMap<>();

        // 1. Set main application data
        response.put("application", application);

        // 2. Get application comments/audit trail
        List<PreApprovalApplicationComments> comments = commentsService.getAuditLogsForApplication(UUID.fromString(applicationId));
        if(!comments.isEmpty()) {
            List<PreApprovalApplicationCommentsDto> commentDtos = comments.stream()
                .map(comment -> {
                    PreApprovalApplicationCommentsDto dto = new PreApprovalApplicationCommentsDto();
                    dto.setId(comment.getId());
                    dto.setApplicationId(comment.getApplication().getId());
                    dto.setAction(comment.getAction());
                    dto.setComments(comment.getComments());
                    dto.setUpdatedBy(comment.getUpdatedBy());
                    dto.setTimestamp(comment.getTimestamp());
                    return dto;
                })
                .collect(Collectors.toList());
            response.put("comments", commentDtos);
        }

        // 3. Add metadata
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("applicationId", applicationId);
        metadata.put("status", application.getStatus());
        metadata.put("state", application.getState());
        metadata.put("applicationNumber", application.getApplicationNumber());
        metadata.put("referenceNumber", application.getReferenceNumber());
        metadata.put("lastModified", application.getLastModifiedDate());
        response.put("metadata", metadata);

        return ResponseEntity.ok(new ApiResponse<>(true, "Record found", response, null));
    }


    @Operation(
            summary = "Get Pre-Approval Applications by Role REST API",
            description = "REST API to fetch Pre-Approval Applications based on various criteria including role, userId, companyId, status, date range, and unified search term. The search parameter will look across multiple fields including companyName, assignee, trainingProvider, courseTitle, vatNumber, referenceNumber, and applicationNumber."
    )
    @GetMapping("/fetch")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getApplicationsByRole(
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) UUID companyId,
            @RequestParam(required = false) String applicationStatus,
            @RequestParam(required = false) String applicationState,
            @RequestParam(required = false) String trainingProvider,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String vatNumber,
            @RequestParam(required = false) String referenceNumber,
            @RequestParam(required = false) String companyName,
            @RequestParam(required = false) String courseTitle,
            @RequestParam(required = false) String assignee,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "0") int pageNumber,
            @RequestParam(defaultValue = "100") int size) {

        log.info("Fetching applications with criteria - role: {}, userId: {}, companyId: {}, status: {}, state: {}, search: {}",
                role, userId, companyId, applicationStatus, applicationState, search);
        
        try {
            // For the frontend, we'll use a special format in the search parameter: "STATUS:STATE"
            if (search != null && search.contains(":")) {
                String[] parts = search.split(":", 2);
                if (parts.length == 2) {
                    // Convert to uppercase to match enum values
                    applicationStatus = parts[0].trim().toUpperCase();
                    applicationState = parts[1].trim().toUpperCase();
                    log.info("Extracted from search parameter: status={}, state={}", applicationStatus, applicationState);
                    
                    // Clear search to avoid double filtering
                    search = null;
                }
            }
            
            // Delegate to service layer 
            Map<String, Object> response = applicationService.getApplicationsByRole(
                    role, userId, companyId, applicationStatus, applicationState, trainingProvider,
                    startDate, endDate, vatNumber,referenceNumber, companyName, courseTitle,
                    assignee, search, pageNumber, size);
            
            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            log.error("Failed to fetch applications with exception: ", e);
            String errorMessage = String.format("Failed to fetch applications: %s", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, errorMessage, null, null));
        }
    }

    @Operation(
            summary = "Check VAT Number in Pre-Approval Application",
            description = "REST API to check if a VAT number exists in a pre-approval application with matching organization and user IDs"
    )
    @PostMapping("/vat/verify")
    public ResponseEntity<ApiResponse<?>> checkVatNumberInApplication(
            @RequestBody Map<String, String> requestBody) {
        try {
            String vatNumber = requestBody.get("vatNumber");
            String applicationId = requestBody.get("applicationId");
            UUID organisationId = UUID.fromString(requestBody.get("organisationId"));
            UUID userId = UUID.fromString(requestBody.get("userId"));

            log.info("Checking VAT number {} for application {} with organization {} and user {}",
                    vatNumber, applicationId, organisationId, userId);

            // Get the application by VAT number
            Optional<PreApprovalApplication> application = applicationService.getPreApprovalApplicationEntityByVatNumber(vatNumber);

            if (application.isPresent() && !Boolean.TRUE.equals(application.get().getDeleted())) {
                // Check if the application ID matches (if provided)
                if (applicationId != null && !applicationId.isEmpty()) {
                    if (!application.get().getId().toString().equals(applicationId)) {
                        return ResponseEntity.ok(new ApiResponse<>(false,
                            "VAT number exists but belongs to a different application", null, null));
                    }
                }

                // Check organization and user ID match
                if (application.get().getOrganisationId().equals(organisationId) &&
                    application.get().getUserId().equals(userId)) {

                    Map<String, Object> response = new HashMap<>();
                    response.put("applicationId", application.get().getId());
                    response.put("vatNumber", application.get().getVatNumber());
                    response.put("applicationNumber", application.get().getApplicationNumber());
                    response.put("referenceNumber", application.get().getReferenceNumber());
                    response.put("status", application.get().getStatus());
                    response.put("state", application.get().getState());

                    return ResponseEntity.ok(new ApiResponse<>(true,
                        "VAT number found in pre-approval application", response, null));
                } else {
                    return ResponseEntity.ok(new ApiResponse<>(false,
                        "VAT number exists but organization or user ID does not match", null, null));
                }
            } else {
                return ResponseEntity.ok(new ApiResponse<>(false,
                    "VAT number not found in pre-approval applications", null, null));
            }
        } catch (Exception e) {
            log.error("Error checking VAT number: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, e.getMessage(), null, null));
        }
    }

    private boolean isValidRole(String role) {
        try {
            UserRoles.valueOf(role.toUpperCase());
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    private boolean isValidUserId(String userId) {
        try {
            UUID.fromString(userId);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    @Operation(
            summary = "Update Paperless ID for Pre-Approval Application REST API",
            description = "REST API to update the paperless ID of a Pre-Approval Application by application ID"
    )
    @PutMapping("/{applicationId}/paperless-id")
    public ResponseEntity<ApiResponse<?>> updatePaperlessId(
            @PathVariable UUID applicationId,
            @RequestBody Map<String, String> payload) {
        
        log.info("Updating paperlessId for application ID: {}", applicationId);
        
        try {
            String paperlessId = payload.get("paperlessId");
            
            if (paperlessId == null || paperlessId.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "PaperlessId is required", null, null));
            }
            
            boolean updated = applicationService.updatePaperlessIdToPreApprovalApplication(
                applicationId.toString(), paperlessId);
            
            if (updated) {
                Map<String, Object> response = new HashMap<>();
                response.put("applicationId", applicationId);
                response.put("paperlessId", paperlessId);
                
                return ResponseEntity.ok(new ApiResponse<>(true, 
                    "Paperless ID updated successfully", response, null));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "Failed to update paperlessId", null, null));
            }
        } catch (ResourceNotFoundException e) {
            log.error("Application not found: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new ApiResponse<>(false, e.getMessage(), null, null));
        } catch (Exception e) {
            log.error("Error updating paperlessId: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, e.getMessage(), null, null));
        }
    }
    

   
}
