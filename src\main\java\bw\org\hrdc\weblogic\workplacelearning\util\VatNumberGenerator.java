package bw.org.hrdc.weblogic.workplacelearning.util;

import java.util.UUID;

/**
 * Utility class for generating VAT numbers.
 */
public class VatNumberGenerator {

    /**
     * Generates a unique VAT number with the format: BW-XXXXXX
     * where XXXXXX is a random 6-character string from UUID.
     *
     * @return A unique VAT number
     */
    public static String generateVatNumber() {
        String randomPart = UUID.randomUUID().toString().replace("-", "").substring(0, 6).toUpperCase();
        return String.format("BW-%s", randomPart);
    }
} 