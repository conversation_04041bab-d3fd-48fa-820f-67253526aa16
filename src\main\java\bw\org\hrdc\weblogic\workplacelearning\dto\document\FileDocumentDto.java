package bw.org.hrdc.weblogic.workplacelearning.dto.document;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.UUID;

/**
 * DTO for representing documents uploaded by an organisation
 */
@Data
@Schema(
        name = "FileDocument",
        description = "Schema representing document metadata for organisation uploads"
)
public class FileDocumentDto {
    @Schema(description = "Unique identifier for the document", example = "7a3a95ab-e3e1-40b7-bdc1-c8e2b1234567")
    private String id;

    @NotNull(message = "Key is required")
    @Schema(description = "Unique file key from the storage system", example = "uploads/org-123/cert-2025.pdf")
    private String key;

    @NotNull(message = "Document name is required")
    @Schema(description = "Original name of the uploaded document", example = "TaxCertificate2025")
    private String docName;

    @NotNull(message = "Document extension is required")
    @Schema(description = "File extension of the document", example = "pdf")
    private String docExt;

    @NotNull(message = "Identifier (Organisation ID) is required")
    @Schema(description = "Organisation identifier", example = "org-*********")
    private String identifier;

    @NotNull(message = "File type is required")
    @Schema(description = "Type of the document", example = "CERTIFICATION")
    private String fileType;

    @NotNull(message = "Document size is required")
    @Schema(description = "Size of the document in bytes", example = "24576")
    private Long docSize;

    @NotNull(message = "File URL is required")
    @Schema(description = "URL to access the uploaded file", example = "https://files.hrdf.bw/uploads/org-123/cert-2025.pdf")
    private String fileUrl;

    @NotNull(message = "InputIdentifier is required")
    @Schema(description = "form input identifier for the uploaded file", example = "CertificateOfAccreditation")
    private String inputIdentifier;

    @NotNull(message = "entityId is required")
    @Schema(description = "entityId for the uploaded file", example = "7a3a95ab-e3e1-40b7-bdc1-c8e2b1234567")
    private UUID entityId;
}
