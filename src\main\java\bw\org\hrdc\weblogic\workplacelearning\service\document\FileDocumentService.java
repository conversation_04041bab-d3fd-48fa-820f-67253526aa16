package bw.org.hrdc.weblogic.workplacelearning.service.document;

import bw.org.hrdc.weblogic.workplacelearning.dto.document.FileDocumentDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.document.FileDocument;
import bw.org.hrdc.weblogic.workplacelearning.mapper.document.FileDocumentMapper;
import bw.org.hrdc.weblogic.workplacelearning.repository.document.FileDocumentRepository;
import bw.org.hrdc.weblogic.workplacelearning.service.NotificationService;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FileDocumentService {

    private static final Logger log = LoggerFactory.getLogger(NotificationService.class);
    private final FileDocumentRepository fileDocumentRepository;
    private final FileDocumentMapper fileDocumentMapper;

    public List<FileDocumentDto> saveDocuments(String entityId, Enums.FileEntityType entityType, List<FileDocumentDto> dtoList) {
        List<FileDocument> documents = dtoList.stream()
                .map(dto -> {

                    if (entityId == null || entityId.isBlank()) {
                        log.error("Invalid FileDocumentDto.entityId: {}", entityId);
                        throw new IllegalArgumentException("Invalid identifier: must not be null or blank");
                    }

                    try {

                        FileDocument entity = fileDocumentMapper.toEntity(dto);
                        entity.setEntityId(UUID.fromString(entityId));
                        entity.setEntityType(entityType);
                        return entity;

                    } catch (IllegalArgumentException e) {
                        log.error("Invalid UUID string: {}", entityId);
                        throw e;
                    }
                })
                .collect(Collectors.toList());

        List<FileDocument> saved = fileDocumentRepository.saveAll(documents);
        return saved.stream().map(fileDocumentMapper::toDto).collect(Collectors.toList());
    }
}