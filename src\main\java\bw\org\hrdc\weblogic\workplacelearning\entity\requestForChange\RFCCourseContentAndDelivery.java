package bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.util.UUID;

@Entity
@Table(name = "rfc_course_content_and_delivery")
@EntityListeners(AuditingEntityListener.class)
@Getter
@Setter
@ToString
public class RFCCourseContentAndDelivery extends Base {

    @Column(nullable = false)
    private String exitLevelOutcomes;

    @Column(nullable = false)
    private String shortCourseDeliveryMode;

    @Column(nullable = false)
    private String learningOutcomes;

    @Column(nullable = false)
    private String learningOutcomesSummary;

    @Column(nullable = false)
    private String location;

    @Column(nullable = false)
    private String shortCourseDeliveryType;

    @OneToOne
    @JoinColumn(name = "application_id", nullable = false)
    @JsonBackReference
    @ToString.Exclude
    private RequestForSignificantChanges application;
}
