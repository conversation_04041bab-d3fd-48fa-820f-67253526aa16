package bw.org.hrdc.weblogic.workplacelearning.mapper.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.RFCDetailsOfSignificantChangeDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RFCDetailsOfSignificantChange;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RequestForSignificantChanges;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Mapper for converting between DetailsOfSignificantChange entity and DetailsOfSignificantChangeDto.
 */
@Component
public class RFCDetailsOfSignificantChangeMapper {

    // Convert Entity to DTO
    public static RFCDetailsOfSignificantChangeDto toDto(RFCDetailsOfSignificantChange entity) {
        if (entity == null) {
            return null;
        }

        RFCDetailsOfSignificantChangeDto dto = new RFCDetailsOfSignificantChangeDto();
        dto.setUuid(UUID.fromString(entity.getUuid()));
        dto.setRequestForSignificantChangesId(UUID.fromString(String.valueOf(entity.getApplication().getUuid())));
        dto.setCriteria(entity.getCriteria());
        dto.setDetails(entity.getDetails());

        return dto;
    }

    // Convert DTO to Entity
    public static RFCDetailsOfSignificantChange toEntity(RFCDetailsOfSignificantChangeDto dto, RequestForSignificantChanges application) {
        if (dto == null) {
            return null;
        }

        RFCDetailsOfSignificantChange entity = new RFCDetailsOfSignificantChange();
//        entity.setUuid(String.valueOf(dto.getUuid()));
        entity.setApplication(application); // Set parent reference
        entity.setCriteria(dto.getCriteria());
        entity.setDetails(dto.getDetails());

        return entity;
    }

    // Convert List<DetailsOfSignificantChange> to List<DetailsOfSignificantChangeDto>
    public static List<RFCDetailsOfSignificantChangeDto> toDtoList(List<RFCDetailsOfSignificantChange> entityList) {
        if (entityList == null) {
            return Collections.emptyList();
        }
        return entityList.stream()
                .map(RFCDetailsOfSignificantChangeMapper::toDto)
                .collect(Collectors.toList());
    }

    // Convert List<DetailsOfSignificantChangeDto> to List<DetailsOfSignificantChange>
    public static List<RFCDetailsOfSignificantChange> toEntityList(List<RFCDetailsOfSignificantChangeDto> dtoList, RequestForSignificantChanges application) {
        if (dtoList == null) {
            return Collections.emptyList();
        }
        return dtoList.stream()
                .map(dto -> RFCDetailsOfSignificantChangeMapper.toEntity(dto, application))
                .collect(Collectors.toList());
    }
}
