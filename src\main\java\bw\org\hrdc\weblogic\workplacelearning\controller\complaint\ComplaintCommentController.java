package bw.org.hrdc.weblogic.workplacelearning.controller.complaint;

import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.CommentPayload;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.Comment;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.CommentDocument;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.CommentDocumentRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.CommentRepository;
import bw.org.hrdc.weblogic.workplacelearning.service.complaint.ComplaintService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.inject.Inject;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse.getInternalServerError;

/**
 * <AUTHOR>
 * @CreatedOn 27/03/25 23:22
 * @UpdatedBy martinspectre
 * @UpdatedOn 27/03/25 23:22
 */
@RestController
@RequestMapping("/api/v1/complaints/{complaintId}/comments")
@RequiredArgsConstructor
public class ComplaintCommentController {
    private static final Logger logger = LoggerFactory.getLogger(ComplaintCommentController.class);
    
    private final CommentRepository commentRepository;
    private final CommentDocumentRepository attachmentRepository;
    @Inject
    private final ComplaintService complaintService;

    @PostMapping
    public ResponseEntity<?> addComment(@PathVariable String complaintId, @RequestBody CommentPayload payload, @RequestHeader("X-Authenticated-User") String userId) {

        try {
            ComplaintEntity complaint = complaintService.fetchById(complaintId);
            if(complaint != null){
                // Check if complaint state is SUBMITTED and update to UNDER_REVIEW if true
                if (Enums.ComplaintState.SUBMITTED.equals(complaint.getState())) {
                    logger.info("Complaint state is SUBMITTED, updating to UNDER_REVIEW");
                    // Update complaint state to UNDER_REVIEW
                    complaint = complaintService.updateStatus(
                            complaintId, 
                            userId, 
                            payload.getComplaintStatus(), // Use status from payload
                            Enums.ComplaintState.UNDER_REVIEW // Set state to UNDER_REVIEW
                    );
                    logger.info("Complaint state updated to UNDER_REVIEW and status to {}", payload.getComplaintStatus());
                } else {
                    // If complaint state is not SUBMITTED, only update the status if provided in payload
                    if (payload.getComplaintStatus() != null && !payload.getComplaintStatus().equals(complaint.getStatus())) {
                        logger.info("Updating complaint status to {}", payload.getComplaintStatus());
                        complaint = complaintService.updateStatus(
                                complaintId,
                                userId,
                                payload.getComplaintStatus(),
                                complaint.getState() // Keep the current state
                        );
                        logger.info("Complaint status updated to {}", payload.getComplaintStatus());
                    }
                }
                
                // Save the comment
                Comment comment = payload.getComment();
                comment.setComplaint(complaint);
                comment.setCreatedBy(userId);
                comment.setUpdatedBy(userId);
                Comment savedComment = commentRepository.save(comment);

                if (payload.getDocuments() != null) {
                    List<CommentDocument> documents = payload.getDocuments();
                    for (CommentDocument document : documents) {
                        CommentDocument attachment = new CommentDocument(
                                savedComment,
                                document.getKey(),
                                document.getDocName(),
                                document.getDocExt(),
                                document.getIdentifier(),
                                document.getFileType(),
                                document.getDocSize(),
                                document.getFileUrl());
                        attachmentRepository.save(attachment);
                    }
                }
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(true, "Comment submitted successfully", null, null));

            } else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Complaint not found", null, null));
            }
        } catch (Exception e) {
            logger.error("Error adding comment: {}", e.getMessage(), e);
            return getInternalServerError(e.getMessage());
        }
    }
}

