package bw.org.hrdc.weblogic.workplacelearning.mapper;

import bw.org.hrdc.weblogic.workplacelearning.dto.AcknowledgementDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.Acknowledgement;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between Acknowledgement entity and AcknowledgementDto.
 */
@Component
public class AcknowledgementMapper {

    private final ModelMapper modelMapper;

    @Autowired
    public AcknowledgementMapper(ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
    }

    // Convert Entity to DTO
    public AcknowledgementDto toDto(Acknowledgement entity) {
        return modelMapper.map(entity, AcknowledgementDto.class);
    }

    // Convert DTO to Entity
    public Acknowledgement toEntity(AcknowledgementDto dto) {
        return modelMapper.map(dto, Acknowledgement.class);
    }

    // Convert Page<Acknowledgement> to Page<AcknowledgementDto>
    public Page<AcknowledgementDto> toDtoPage(Page<Acknowledgement> entityPage) {
        return entityPage.map(this::toDto);
    }

    // Convert List<Acknowledgement> to List<AcknowledgementDto>
    public List<AcknowledgementDto> toDtoList(List<Acknowledgement> entityList) {
        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
