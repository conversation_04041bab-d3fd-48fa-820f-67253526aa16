package bw.org.hrdc.weblogic.workplacelearning.service.impl;

import bw.org.hrdc.weblogic.workplacelearning.dto.ETPEvaluationDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ETPEvaluation;
import bw.org.hrdc.weblogic.workplacelearning.exception.ApplicationNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.mapper.ETPEvaluationMapper;
import bw.org.hrdc.weblogic.workplacelearning.repository.ETPEvaluationRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.ETPEvaluationSpecification;
import bw.org.hrdc.weblogic.workplacelearning.service.IETPEvaluationService;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.UUID;

/**
 * Implementation of the service interface for ETPEvaluation.
 */
@Service
@AllArgsConstructor
public class ETPEvaluationServiceImpl implements IETPEvaluationService {

    private static final Logger log = LoggerFactory.getLogger(ETPEvaluationServiceImpl.class);

    private final ETPEvaluationRepository evaluationRepository;
    private final ETPEvaluationMapper evaluationMapper;

    @Override
    @Transactional
    public void createEvaluation(ETPEvaluationDto evaluationDto) {
        // Map DTO to Entity
        ETPEvaluation evaluation = evaluationMapper.toEntity(evaluationDto);

        // Link ActionPlans to the parent Evaluation
        if (evaluation.getActionPlans() != null) {
            evaluation.getActionPlans().forEach(actionPlan -> actionPlan.setEvaluation(evaluation));
        }

        // Link CorrectiveActionProgressReports to the parent Evaluation
        if (evaluation.getCorrectiveActionProgressReports() != null) {
            evaluation.getCorrectiveActionProgressReports().forEach(report -> report.setEvaluation(evaluation));
        }

        // Link DiscussionItems to the parent Evaluation
        if (evaluation.getDiscussionItems() != null) {
            evaluation.getDiscussionItems().forEach(discussionItem -> discussionItem.setEtpEvaluation(evaluation));
        }

        // Link ComplianceCriteria and its fields to the parent Evaluation
        if (evaluation.getComplianceCriteria() != null) {
            evaluation.getComplianceCriteria().forEach(criteria -> {
                criteria.setEvaluation(evaluation);
                if (criteria.getFields() != null) {
                    criteria.getFields().forEach(field -> field.setCriteria(criteria));
                }
            });
        }

        // Save the evaluation (this will cascade and save all linked entities)
        evaluationRepository.save(evaluation);

        log.info("ETP Monitoring & Evaluation created with ID: {}", evaluation.getId());
    }


    @Override
    @Transactional
    public void updateEvaluation(ETPEvaluationDto evaluationDto) {
        // Retrieve the existing Evaluation entity
        ETPEvaluation existingEvaluation = evaluationRepository.findById(evaluationDto.getId())
                .orElseThrow(() -> new ApplicationNotFoundException("Evaluation not found with ID: " + evaluationDto.getId()));

        // Map updated fields from DTO to Entity
        ETPEvaluation updatedEvaluation = evaluationMapper.toEntity(evaluationDto);

        // Retain the existing ID
        updatedEvaluation.setId(existingEvaluation.getId());

        // Update ActionPlans
        if (updatedEvaluation.getActionPlans() != null) {
            existingEvaluation.getActionPlans().clear();
            updatedEvaluation.getActionPlans().forEach(actionPlan -> {
                actionPlan.setEvaluation(existingEvaluation);
                existingEvaluation.getActionPlans().add(actionPlan);
            });
        }

        // Update CorrectiveActionProgressReports
        if (updatedEvaluation.getCorrectiveActionProgressReports() != null) {
            existingEvaluation.getCorrectiveActionProgressReports().clear();
            updatedEvaluation.getCorrectiveActionProgressReports().forEach(report -> {
                report.setEvaluation(existingEvaluation);
                existingEvaluation.getCorrectiveActionProgressReports().add(report);
            });
        }

        // Update DiscussionItems
        if (updatedEvaluation.getDiscussionItems() != null) {
            existingEvaluation.getDiscussionItems().clear();
            updatedEvaluation.getDiscussionItems().forEach(discussionItem -> {
                discussionItem.setEtpEvaluation(existingEvaluation);
                existingEvaluation.getDiscussionItems().add(discussionItem);
            });
        }

        // Update ComplianceCriteria and its fields
        if (updatedEvaluation.getComplianceCriteria() != null) {
            existingEvaluation.getComplianceCriteria().clear();
            updatedEvaluation.getComplianceCriteria().forEach(criteria -> {
                criteria.setEvaluation(existingEvaluation);
                if (criteria.getFields() != null) {
                    criteria.getFields().forEach(field -> field.setCriteria(criteria));
                }
                existingEvaluation.getComplianceCriteria().add(criteria);
            });
        }

        // Update other fields
        existingEvaluation.setAssessmentSummary(updatedEvaluation.getAssessmentSummary());
        existingEvaluation.setMeetingDate(updatedEvaluation.getMeetingDate());

        // Save the updated Evaluation entity
        evaluationRepository.save(existingEvaluation);

        log.info("ETP Monitoring & Evaluation updated with ID: {}", existingEvaluation.getId());
    }

    @Override
    public ETPEvaluationDto getEvaluation(UUID id) {
        return evaluationRepository.findById(id)
                .map(evaluationMapper::toDto)
                .orElseThrow(() -> new ApplicationNotFoundException("Evaluation not found with ID: " + id));
    }

    @Override
    public Page<ETPEvaluationDto> fetchAllEvaluations(
            Date meetingDate, UUID userId, UUID organisationId, Pageable pageable) {

        Page<ETPEvaluation> evaluations = evaluationRepository.findAll(
                ETPEvaluationSpecification.searchByCriteria(meetingDate, userId, organisationId), pageable);

        return evaluationMapper.toDtoPage(evaluations);
    }

    @Override
    @Transactional
    public void deleteEvaluation(UUID id) {
        // Check if the evaluation exists
        ETPEvaluation evaluation = evaluationRepository.findById(id)
                .orElseThrow(() -> new ApplicationNotFoundException("Evaluation not found with ID: " + id));

        // Delete the evaluation
        evaluationRepository.delete(evaluation);

        log.info("ETP Monitoring & Evaluation deleted with ID: {}", id);
    }
}
