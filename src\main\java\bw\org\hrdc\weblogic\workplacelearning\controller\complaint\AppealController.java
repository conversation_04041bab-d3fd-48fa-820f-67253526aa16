package bw.org.hrdc.weblogic.workplacelearning.controller.complaint;

import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.*;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintDocument;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import bw.org.hrdc.weblogic.workplacelearning.service.complaint.AppealService;
import bw.org.hrdc.weblogic.workplacelearning.service.complaint.PaperlessService;
import bw.org.hrdc.weblogic.workplacelearning.service.complaint.ComplaintService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums.ComplaintState;
import jakarta.inject.Inject;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import static bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse.getInternalServerError;

/**
 * <AUTHOR>
 * @CreatedOn 07/04/25 03:09
 * @UpdatedBy martinspectre
 * @UpdatedOn 07/04/25 03:09
 */
@RestController
@RequestMapping("/api/v1/appeals")
@RequiredArgsConstructor
public class AppealController {
    private static final Logger logger = LoggerFactory.getLogger(AppealController.class);

    @Inject
    private final AppealService appealService;
    
    @Inject
    private final PaperlessService paperlessService;

    @Inject
    private final WorkflowClient workflowClient;

    @Inject
    private final ComplaintService complaintService;



    @PostMapping
    public ResponseEntity<?> createAppeal(@RequestBody() RequestPayload payload, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Appeal creation initiated by: {}", userId);
        try {
            
            AppealCreationResponse savedAppeal = appealService.createAppealWithRejectorInfo(payload.getDetails(), userId);

            if(savedAppeal != null){
                // Handle document attachments
                if (payload.getDocuments() != null) {
                    List<ComplaintDocument> documents = payload.getDocuments();
                    for (ComplaintDocument document : documents) {
                        ComplaintEntity appealEntity = appealService.fetchById(savedAppeal.getUuid());
                        document.setComplaint(appealEntity);
                        appealService.createAppealDocument(document);
                    }
                }

                Optional<ComplaintEntity> response = Optional.ofNullable(appealService.fetchById(savedAppeal.getUuid()));
                //TODO Send an email to client contact about the created appeal with reference time and resolution time
                System.out.println("Appeal created with reference number: " + savedAppeal.getReferenceNumber());
                
                // Start workflow process
                try {
                    Map<String, Object> workflowResponse = workflowClient.getAppealWorkflow(savedAppeal.getUuid());

                    System.out.println("Workflow Response: " + workflowResponse);
                    logger.info("Workflow response: {}", workflowResponse);
                    
                    if (workflowResponse != null && Boolean.TRUE.equals(workflowResponse.get("success"))) {
                        // Extract processInstanceId directly from the workflow response
                        String processInstanceId = (String) workflowResponse.get("processInstanceId");
                        String appealId = response.get().getUuid();

                        if (processInstanceId != null && appealId != null) {
                            System.out.println("Process Instance ID: " + processInstanceId);
                            System.out.println("appealId: " + appealId);
                            
                            // Save the processInstanceId to the database
                            complaintService.updateProcessInstanceId(appealId,processInstanceId);
                            response.get().setProcessInstanceId(processInstanceId);
                            logger.info("Process instance ID {} saved for appeal {}", processInstanceId, appealId);
                        } else {
                            logger.warn("Missing processInstanceId or appealId - processInstanceId: {}, appealId: {}", 
                                      processInstanceId, appealId);
                        }
                    } else {
                        logger.warn("Workflow response was unsuccessful or null");
                    }
                    
                } catch (Exception e) {
                    logger.error("Failed to start appeal workflow: {}", e.getMessage());
                    // Continue with appeal creation even if workflow fails
                }
                
                // Create a simplified response with only the required fields
                if (response.isPresent()) {
                    ComplaintEntity appeal = response.get();
                    Map<String, Object> simplifiedResponse = new LinkedHashMap<>();
                    
                    // Include only the requested fields
                    simplifiedResponse.put("uuid", appeal.getUuid());
                    simplifiedResponse.put("department", appeal.getDepartment());
                    simplifiedResponse.put("reference", appeal.getReference());
                    simplifiedResponse.put("referenceNumber", appeal.getReferenceNumber());
                    simplifiedResponse.put("status", appeal.getStatus());
                    simplifiedResponse.put("state", appeal.getState());
                    simplifiedResponse.put("assignee", appeal.getAssignee());
                    simplifiedResponse.put("assigneeRole", appeal.getAssigneeRole());
                    simplifiedResponse.put("CreatedAt", appeal.getCreatedAt());
                    
                    return ResponseEntity.ok(new ApiResponse<>(true, "Appeal created successfully", simplifiedResponse, null));
                } else {
                    return ResponseEntity.ok(new ApiResponse<>(true, "Appeal created successfully", null, null));
                }
            }else {
                logger.error("Appeal creation failed");
                return ApiResponse.createErrorResponse("APPEAL_FILING_ERROR", "Failed to create appeal.");
            }
        } catch (Exception e) {
            logger.error("Appeal creation failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<?>> getAllComplaints(@PathVariable Integer offset, @PathVariable Integer pageSize) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Page<ListData> applications = appealService.fetchList(PageRequest.of(offset, pageSize));

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<?>> getAppealById(@PathVariable String id) {
        try {
            ComplaintResponse complaint = appealService.fetchDetailedAppeal(id);
            if(complaint != null){
                return ResponseEntity.ok(new ApiResponse<>(true, "Record found", complaint, null));

            }else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "No record found", null, null));

            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<?> updateStatus(@PathVariable String id, @RequestBody ComplaintActionRequest request, @RequestHeader("X-Authenticated-User") String userId) {

        try {
            ComplaintState action = request.getAction();
            ComplaintEntity complaint = appealService.updateStatus(id, action, userId);
            if(complaint != null){
                //TODO send email if the complain is closed or escalated
                return ResponseEntity.ok(new ApiResponse<>(true, "Appeal updated", complaint, null));

            }else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Error updating appeal status", null, null));

            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{offset}/{pageSize}/{companyId}")
    public ResponseEntity<ApiResponse<?>> getAllCompanyAppealss(@PathVariable Integer offset, @PathVariable Integer pageSize, @PathVariable String companyId) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Sort sort = Sort.by("status").descending();
            Pageable pageable = PageRequest.of(offset, pageSize, sort);

            Page<ListData> applications = appealService.fetchCompanyList(companyId, pageable);

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{companyId}/statistics")
    public ResponseEntity<?> getCompanyStatistics(@PathVariable String companyId) {
        try {
            ComplaintsStats statistics =  appealService.getCompanyStatistics(companyId);
            return ResponseEntity.ok(new ApiResponse<>(true, "Statistical data", statistics, null));
        } catch (Exception e) {
            logger.error("Failed to fetch company statistical data with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/statistics")
    public ResponseEntity<?> getAllStatistics() {
        try {
            ComplaintsStats statistics =  appealService.getStatistics();
            return ResponseEntity.ok(new ApiResponse<>(true, "Statistical data", statistics, null));
        } catch (Exception e) {
            logger.error("Failed to fetch statistical data with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PostMapping("/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<?>> getAllUserAssignedComplaints(@PathVariable Integer offset, @PathVariable Integer pageSize, @RequestBody ComplaintSearchCriteria searchCriteria) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Page<ListData> applications = appealService.fetchUserAssignedList(PageRequest.of(offset, pageSize), searchCriteria);

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @PatchMapping("/{id}/assign-agent/{agentId}")
    public ResponseEntity<?> assignAgent(@PathVariable String id, @PathVariable String agentId, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Appeal agent assignment initiated for complaint id : {}", id);
        try {
            ComplaintEntity complaint = appealService.fetchById(id);
            if(complaint != null){
                complaint.setAssignee(agentId);
                ComplaintEntity updateStatus = appealService.assignedNewAssistance(id, agentId, userId);
                if (updateStatus == null) {
                    logger.error("Failed to assign appeal {} to agent", id);
                    return ApiResponse.createErrorResponse("APPEAL_UPDATE_ERROR", "Failed to process appeal action.");
                }else{
                    //TODO trigger notification to agent about the reassignment
                    //TODO trigger notification to agent for the assignment made

                    return ResponseEntity.ok(new ApiResponse<>(true, "Agent assigned successfully", null, null));
                }
            }else{
                return ApiResponse.createErrorResponse("APPEAL_NOT_FOUND", "Provided appeal id does not exist");
            }
        } catch (Exception e) {
            logger.error("Failed to assign appeal id {} to agent id {} with exception: {}", id, agentId, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }
    
    /**
     * Manager closes appeal with specific action (approve or reject)
     */
    @PatchMapping("/{id}/statusupdate")
    public ResponseEntity<?> closeAppeal(
            @PathVariable String id, 
            @RequestBody ComplaintActionRequest payload,
            @RequestHeader("X-Authenticated-User") String userId) {
        
        logger.info("Manager close appeal initiated for appeal id: {} with action: {}", id, payload.getAction());
        
        try {
            ComplaintEntity appeal = appealService.fetchById(id);
            if (appeal == null) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("statusCode", "ERROR");
                errorResponse.put("statusMsg", "Appeal not found");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }
            
            // Validate action
            ComplaintState action = payload.getAction();
            if (action == null || 
                    (action != ComplaintState.COMPLETED && action != ComplaintState.REJECTED)) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("statusCode", "ERROR");
                errorResponse.put("statusMsg", "Invalid action. Must be either 'COMPLETED' or 'REJECTED'");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            
            ComplaintEntity updatedAppeal = appealService.updateStatus(id, action, userId);
        
            // Update original application status based on appeal decision
            boolean applicationUpdated = appealService.updateOriginalApplicationStatus(appeal, action.name());
            
            // Add reason to audit log if provided
            if (payload.getReason() != null && !payload.getReason().trim().isEmpty()) {
                appealService.addAuditLog(appeal, "COMMENT", payload.getReason(), userId);
            }
            
            // Resume workflow process if processInstanceId exists
            try {
                String processInstanceId = appeal.getProcessInstanceId();
                if (processInstanceId != null && !processInstanceId.trim().isEmpty()) {
                    // Prepare workflow payload
                    Map<String, Object> workflowPayload = new HashMap<>();
                    workflowPayload.put("appealId", id);
                    workflowPayload.put("state", action.name());
                    workflowPayload.put("status", updatedAppeal.getStatus());
                    workflowPayload.put("decision", action == ComplaintState.COMPLETED ? "close" : "reject");
                    workflowPayload.put("userId", userId);
                    workflowPayload.put("reason", payload.getReason());
                    
                    // Resume workflow
                    Map<String, Object> workflowResponse = workflowClient.resumeAppealProcess(processInstanceId, workflowPayload);
                    
                    logger.info("Workflow resume response: {}", workflowResponse);
                    System.out.println("Workflow resume response: " + workflowResponse);
                    
                    if (workflowResponse != null && Boolean.TRUE.equals(workflowResponse.get("success"))) {
                        logger.info("Appeal workflow resumed successfully for appeal: {} with decision: {}", id, action.name());
                    } else {
                        logger.warn("Failed to resume appeal workflow for appeal: {} - Response: {}", id, workflowResponse);
                    }
                } else {
                    logger.warn("No processInstanceId found for appeal: {}, workflow not resumed", id);
                }
            } catch (Exception e) {
                logger.error("Failed to resume appeal workflow for appeal: {} - Error: {}", id, e.getMessage());
                // Continue processing even if workflow fails
            }
            
            Map<String, String> response = new HashMap<>();
            response.put("statusCode", "SUCCESS");
            response.put("statusMsg", "Appeal status updated successfully");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to close appeal id {} with exception: {}", id, e.getMessage());
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("statusCode", "ERROR");
            errorResponse.put("statusMsg", "Failed to update application status: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

   
    /**
     *  ADD: Fetch appeals by role 
     */
    @GetMapping("/fetch")
    public ResponseEntity<ApiResponse<?>> getAppealsByRole(
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String companyId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String assignedTo,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "0") int pageNumber,
            @RequestParam(defaultValue = "100") int size,
            @RequestParam(required = false, defaultValue = "createdAt") String sortBy,
            @RequestParam(required = false, defaultValue = "DESC") String direction) {

        logger.info("Fetching appeals with criteria - role: {}, userId: {}, companyId: {}, status: {}, state: {}, assignedTo: {}, search: {}",
                role, userId, companyId, status, state, assignedTo, search);

        try {
            if (size <= 0) {
                size = 10;
            }

            // Build search criteria from query parameters
            ComplaintSearchCriteria searchCriteria = new ComplaintSearchCriteria();
            searchCriteria.setRole(role);
            searchCriteria.setAssignedTo(userId); // Use userId for role-based filtering
            searchCriteria.setStatus(status);
            searchCriteria.setState(state);
            searchCriteria.setSortBy(sortBy);
            searchCriteria.setDirection(direction);

            logger.info("Role-based filtering: role={}, userId={}", role, userId);

            Page<ListData> applications = appealService.fetchUserAssignedList(PageRequest.of(pageNumber, size), searchCriteria);

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            logger.error("Failed to fetch appeals with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

}
