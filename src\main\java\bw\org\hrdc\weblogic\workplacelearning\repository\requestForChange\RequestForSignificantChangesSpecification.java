package bw.org.hrdc.weblogic.workplacelearning.repository.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RequestForSignificantChanges;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

/**
 * Specification for RequestForSignificantChanges entity.
 */
public class RequestForSignificantChangesSpecification {

    /**
     * Creates a specification for filtering RequestForSignificantChanges by various criteria.
     *
     * @param applicationStatus        the status of the request (e.g., Pending, Approved, Rejected).
     * @param requestedBy              the username or identifier of the requester.
     * @param isChangeOfTitle            whether the request involves a change of title.
     * @param isChangeOfCourseDuration   whether the request involves a change in course duration.
     * @return a specification for filtering RequestForSignificantChanges.
     */
    public static Specification<RequestForSignificantChanges> searchByCriteria(
            String applicationStatus,
            String requestedBy,
            Boolean isChangeOfTitle,
            Boolean isChangeOfCourseDuration
    ) {
        return (root, query, cb) -> {
            Predicate p = cb.conjunction();

            if (applicationStatus != null && !applicationStatus.isEmpty()) {
                p = cb.and(p, cb.equal(root.get("requestStatus"), applicationStatus));
            }
            if (requestedBy != null && !requestedBy.isEmpty()) {
                p = cb.and(p, cb.equal(root.get("requestedBy"), requestedBy));
            }
            if (isChangeOfTitle != null) {
                p = cb.and(p, cb.equal(root.get("is_change_of_title"), isChangeOfTitle));
            }
            if (isChangeOfCourseDuration != null) {
                p = cb.and(p, cb.equal(root.get("change_of_course_duration"), isChangeOfCourseDuration));
            }

            return p;
        };
    }
}
