package bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.ShortCourseInformationDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ShortCourseInformation;
import java.util.Set;
import java.util.stream.Collectors;

public class ShortCourseInformationMapper{

    public static ShortCourseInformation toEntity(ShortCourseInformationDto dto, NCBSCApplication application) {
        if (dto == null) {
            return null;
        }

        ShortCourseInformation entity = new ShortCourseInformation();

        entity.setType(dto.getType());
        entity.setFieldOfLearning(dto.getFieldOfLearning());
        entity.setCourseLearningTime(dto.getCourseLearningTime());
        entity.setStartDate(dto.getStartDate());
        entity.setEndDate(dto.getEndDate());
        entity.setYearDueForReview(dto.getYearDueForReview());
        entity.setTargetPopulation(dto.getTargetPopulation());
        entity.setEntryRequirements(dto.getEntryRequirements());
        entity.setTitle(dto.getTitle());
        entity.setDuration(dto.getDuration());
        entity.setApplication(application);
        entity.setSubFieldOfLearning(dto.getSubFieldOfLearning());
        entity.setLevel(dto.getLevel());
        entity.setAccreditingBody(dto.getAccreditingBody());

        return entity;
    }

    public static ShortCourseInformationDto toDto(ShortCourseInformation entity) {
        if (entity == null) {
            return null;
        }

        ShortCourseInformationDto model = new ShortCourseInformationDto();

        model.setType(entity.getType());
        model.setFieldOfLearning(entity.getFieldOfLearning());
        model.setCourseLearningTime(entity.getCourseLearningTime());
        model.setStartDate(entity.getStartDate());
        model.setEndDate(entity.getEndDate());
        model.setYearDueForReview(entity.getYearDueForReview());
        model.setTargetPopulation(entity.getTargetPopulation());
        model.setEntryRequirements(entity.getEntryRequirements());
        model.setTitle(entity.getTitle());
        model.setDuration(entity.getDuration());
        model.setSubFieldOfLearning(entity.getSubFieldOfLearning());
        model.setLevel(entity.getLevel());
        model.setAccreditingBody(entity.getAccreditingBody());

        return model;
    }

    public static Set<ShortCourseInformation> toEntityList(Set<ShortCourseInformationDto> dto, NCBSCApplication application) {
        if (dto == null) {
            return null;
        }

        return dto.stream().map(scope -> ShortCourseInformationMapper.toEntity(scope, application)).collect(Collectors.toSet());
    }

    public static Set<ShortCourseInformationDto> toDtoList(Set<ShortCourseInformation> entity) {
        if (entity == null) {
            return null;
        }

        return entity.stream().map(ShortCourseInformationMapper::toDto).collect(Collectors.toSet());
    }
}
