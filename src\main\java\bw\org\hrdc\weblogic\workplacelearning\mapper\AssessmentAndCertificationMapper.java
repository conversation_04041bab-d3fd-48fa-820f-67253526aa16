package bw.org.hrdc.weblogic.workplacelearning.mapper;

import bw.org.hrdc.weblogic.workplacelearning.dto.AssessmentAndCertificationDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.AssessmentAndCertification;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between AssessmentAndCertification entity and AssessmentAndCertificationDto.
 */
@Component
public class AssessmentAndCertificationMapper {

    private final ModelMapper modelMapper;

    @Autowired
    public AssessmentAndCertificationMapper(ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
    }

    // Convert Entity to DTO
    public AssessmentAndCertificationDto toDto(AssessmentAndCertification entity) {
        return modelMapper.map(entity, AssessmentAndCertificationDto.class);
    }

    // Convert DTO to Entity
    public AssessmentAndCertification toEntity(AssessmentAndCertificationDto dto) {
        return modelMapper.map(dto, AssessmentAndCertification.class);
    }

    // Convert Page<AssessmentAndCertification> to Page<AssessmentAndCertificationDto>
    public Page<AssessmentAndCertificationDto> toDtoPage(Page<AssessmentAndCertification> entityPage) {
        return entityPage.map(this::toDto);
    }

    // Convert List<AssessmentAndCertification> to List<AssessmentAndCertificationDto>
    public List<AssessmentAndCertificationDto> toDtoList(List<AssessmentAndCertification> entityList) {
        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
