package bw.org.hrdc.weblogic.workplacelearning.service;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import bw.org.hrdc.weblogic.workplacelearning.dto.NonCreditBearingShortCourseApplicationDto;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.UUID;

/**
 * Service interface for NonCreditBearingShortCourseApplication.
 */
public interface INonCreditBearingShortCourseApplicationService {

    /**
     * Creates a new Non-Credit Bearing Short Course Application.
     *
     * @param applicationDto the DTO containing application details.
     */
    void createApplication(NonCreditBearingShortCourseApplicationDto applicationDto);

    /**
     * Updates an existing Non-Credit Bearing Short Course Application.
     *
     * @param applicationDto the DTO containing updated application details.
     */
    void updateApplication(NonCreditBearingShortCourseApplicationDto applicationDto);

    /**
     * Retrieves a Non-Credit Bearing Short Course Application by its ID.
     *
     * @param id the unique identifier of the application.
     * @return the DTO containing application details.
     */
    NonCreditBearingShortCourseApplicationDto getApplication(UUID id);

    /**
     * Fetches all applications based on search criteria and pagination.
     *
     * @param dateSubmitted   the date the application was submitted.
     * @param dateReviewed    the date the application was reviewed.
     * @param applicationStatus the status of the application.
     * @param organisationId  the organisation associated with the application.
     * @param pageable        the pagination and sorting information.
     * @return a paginated list of applications matching the criteria.
     */
    Page<NonCreditBearingShortCourseApplicationDto> fetchAllApplications(
            Date dateSubmitted, Date dateReviewed, ApplicationStatus applicationStatus, UUID organisationId, Pageable pageable);

    @Transactional
    void deleteApplication(UUID id);
}
