package bw.org.hrdc.weblogic.workplacelearning.service.audit;

import bw.org.hrdc.weblogic.workplacelearning.dto.audit.AuditTrailDTO;
import bw.org.hrdc.weblogic.workplacelearning.entity.audit.AuditTrail;
import bw.org.hrdc.weblogic.workplacelearning.repository.audit.AuditTrailRepository;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @CreatedOn 25/02/25 21:47
 * @UpdatedBy martinspectre
 * @UpdatedOn 25/02/25 21:47
 */
@Service
public class AuditLogService {

    private final AuditTrailRepository auditTrailRepository;

    public AuditLogService(AuditTrailRepository auditTrailRepository) {
        this.auditTrailRepository = auditTrailRepository;
    }

    public void save(AuditTrailDTO auditTrailDTO){
        auditTrailRepository.save(new AuditTrail(auditTrailDTO));
    }

}
