package bw.org.hrdc.weblogic.workplacelearning.service.complaint;

import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ApplicationRejectionInfo;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.AppealCreationResponse;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintResponse;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintSearchCriteria;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintsStats;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ListData;
import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.AuditLog;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintDocument;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import bw.org.hrdc.weblogic.workplacelearning.exception.InvalidReferenceException;
import bw.org.hrdc.weblogic.workplacelearning.repository.PreApprovalApplicationRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.AuditLogRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintDocumentRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.noc.NOCApplicationRepo;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition.NCBSCApplicationRepo;
import bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining.WorkPlaceTrainingPlanRepository;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.BaseSpecifications;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums.ComplaintState;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import jakarta.persistence.criteria.Predicate;

/**
 * <AUTHOR>
 * @CreatedOn 07/04/25 02:17
 * @UpdatedBy martinspectre
 * @UpdatedOn 07/04/25 02:17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AppealService {
    private static final Logger logger = LoggerFactory.getLogger(AppealService.class);

    private final ComplaintRepository appealRepository;
    private final AuditLogRepository auditLogRepository;

    private final PaperlessService paperlessService;
    private final ComplaintDocumentRepository documentRepository;
    
    @Autowired
    private CompanyClient companyClient;
    
    @Autowired
    private WorkflowClient workflowClient;
    
    @Autowired
    private PreApprovalApplicationRepository preApprovalRepository;
    
    @Autowired
    private NCBSCApplicationRepo ncbscRepository;
    
    @Autowired
    private NOCApplicationRepo nocRepository;
    
    @Autowired
    private WorkPlaceTrainingPlanRepository workSkillRepository;

    private String generateAppealReference() {
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String uniquePart = String.format("%04d", (int) (Math.random() * 10000));
        return "APP-" + datePart + "-" + uniquePart;
    }

    /**
     *  ADD: Manager assignment with load balancing 
     */
    private String assignManagerByWorkload() {
        try {
            logger.info("Attempting to fetch managers from Account Service for load balancing...");

            ApiResponse<?> response = companyClient.fetchAllActiveUsers("manager");
            logger.info("Account Service response - Status: {}, Data: {}", response.isStatus(), response.getData());

            if (response.isStatus() && response.getData() != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> managerData = (List<Map<String, Object>>) response.getData();
                logger.info("Found {} managers from Account Service", managerData.size());

                if (!managerData.isEmpty()) {
                    String selectedManagerId = findManagerWithLeastWorkload(managerData);
                    logger.info("Assigned appeal to manager with least workload: {}", selectedManagerId);
                    return selectedManagerId;
                }
            }
        } catch (Exception e) {
            logger.warn("Failed to fetch managers from Account Service: {}", e.getMessage());
        }

        throw new RuntimeException("No managers available for appeal assignment. Please ensure Account Service is running and has active managers.");
    }

    /**
     *  ADD: Manager workload calculation (Same pattern as complaint agent workload)
     */
    private String findManagerWithLeastWorkload(List<Map<String, Object>> managers) {
        // Get current appeal workload from database using aggregation query
        List<Object[]> workloadData = appealRepository.getAppealCountByManager();

        // Convert to Map for easy lookup: managerId -> appeal count
        Map<String, Long> workloadMap = workloadData.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],  // assignee (manager ID)
                row -> ((Number) row[1]).longValue()  // appeal count
            ));

        logger.info("Current manager workloads: {}", workloadMap);

        // Find manager with minimum workload (including managers with 0 appeals)
        String selectedManager = managers.stream()
            .map(manager -> (String) manager.get("id"))
            .min(Comparator.comparing(managerId -> workloadMap.getOrDefault(managerId, 0L)))
            .orElse(null);

        Long selectedManagerWorkload = workloadMap.getOrDefault(selectedManager, 0L);
        logger.info("Selected manager {} with {} active appeals", selectedManager, selectedManagerWorkload);

        return selectedManager;
    }

    /**
     * Validates if the reference number is valid for an appeal
     * @param typeOfComplaint The type of complaint (e.g., "preapproval application")
     * @param referenceNumber The reference number to validate
     * @return ApplicationRejectionInfo if valid, throws InvalidReferenceException otherwise
     */
    private ApplicationRejectionInfo validateReferenceNumber(String typeOfComplaint, String referenceNumber) {
        logger.info("Validating reference number {} for type {}", referenceNumber, typeOfComplaint);
        
        if (referenceNumber == null || referenceNumber.trim().isEmpty()) {
            throw new InvalidReferenceException("Reference number is required for appeals");
        }
        
        // Use the common method with rejection validation enabled
        return fetchApplicationInfo(typeOfComplaint, referenceNumber, true);
    }

    /**
     * Determines the role of who rejected the application based on assignment fields
     * Logic:
     * - If only assigned_agent has value (others null) → AGENT
     * - If both assigned_agent and assigned_officer have values → OFFICER
     * - If all 3 (assigned_agent, assigned_officer, assigned_manager) have values → MANAGER
     */
    private String determineRejectionRole(String assignedAgent, String assignedOfficer, String assignedManager) {
        boolean hasAgent = assignedAgent != null && !assignedAgent.trim().isEmpty();
        boolean hasOfficer = assignedOfficer != null && !assignedOfficer.trim().isEmpty();
        boolean hasManager = assignedManager != null && !assignedManager.trim().isEmpty();
        
        if (hasAgent && hasOfficer && hasManager) {
            return "MANAGER";
        } else if (hasAgent && hasOfficer) {
            return "OFFICER";
        } else if (hasAgent) {
            return "AGENT";
        } else {
            // Default case - shouldn't happen in normal flow
            return "UNKNOWN";
        }
    }

    /**
     * Gets the user ID of who rejected the application based on assignment fields
     * Logic:
     * - If only assigned_agent has value (others null) → return assigned_agent ID
     * - If both assigned_agent and assigned_officer have values → return assigned_officer ID
     * - If all 3 (assigned_agent, assigned_officer, assigned_manager) have values → return assigned_manager ID
     */
    private String getRejectedById(String assignedAgent, String assignedOfficer, String assignedManager) {
        boolean hasAgent = assignedAgent != null && !assignedAgent.trim().isEmpty();
        boolean hasOfficer = assignedOfficer != null && !assignedOfficer.trim().isEmpty();
        boolean hasManager = assignedManager != null && !assignedManager.trim().isEmpty();
        
        if (hasAgent && hasOfficer && hasManager) {
            return assignedManager;  // Manager rejected
        } else if (hasAgent && hasOfficer) {
            return assignedOfficer;  // Officer rejected
        } else if (hasAgent) {
            return assignedAgent;    // Agent rejected
        } else {
            return null;  // No rejector found
        }
    }

    /**
     *  NEW: Enhanced appeal creation with rejector information
     */
    public AppealCreationResponse createAppealWithRejectorInfo(ComplaintEntity appeal, String userId) {
        ApplicationRejectionInfo rejectionInfo = null;
        
        // Validate the reference number if provided
        if (appeal.getReference() != null && !appeal.getReference().trim().isEmpty()) {
            try {
                rejectionInfo = validateReferenceNumber(
                        appeal.getTypeOfComplaint(), appeal.getReference());
        
                
                logger.info("Reference validation successful for {}", appeal.getReference());
            } catch (InvalidReferenceException e) {
                logger.error("Reference validation failed: {}", e.getMessage());
                throw e;
            }
        }
        
        String referenceNumber = generateAppealReference();

        //  Automatic manager assignment with load balancing
        String assignedManagerId = assignManagerByWorkload();

        appeal.setStatus(Enums.ComplaintStatus.OPEN);
        appeal.setState(Enums.ComplaintState.SUBMITTED);
        appeal.setReferenceNumber(referenceNumber);
        appeal.setAssignee(assignedManagerId);
        appeal.setAssigneeRole(Enums.UserRoles.MANAGER);
        appeal.setCategory(Enums.CategoryComplaint.APPEAL);

        ComplaintEntity savedAppeal = appealRepository.save(appeal);

        AuditLog log = new AuditLog(savedAppeal, "CREATED", "Appeal submitted and assigned to manager", userId, LocalDateTime.now());
        auditLogRepository.save(log);

        //  Build response with rejector information
        return buildAppealCreationResponse(savedAppeal, rejectionInfo);
    }

    public ComplaintEntity fetchById(String appealId) {
        return appealRepository.findByUuid(appealId)
                .orElseThrow(() -> new RuntimeException("Appeal not found"));
    }
    
    /**
     * Adds an audit log entry for an appeal
     * @param appeal The appeal entity
     * @param action The action performed
     * @param description Description of the action
     * @param userId User who performed the action
     * @return The created audit log
     */
    public AuditLog addAuditLog(ComplaintEntity appeal, String action, String description, String userId) {
        AuditLog log = new AuditLog(appeal, action, description, userId, LocalDateTime.now());
        return auditLogRepository.save(log);
    }

    public ComplaintResponse fetchDetailedAppeal(String appealId){
        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }

        Optional<ComplaintEntity> appeal = appealRepository.findByUuid(appealId);

        if(appeal.isPresent()){

            ComplaintEntity appealData = appeal.get();
            ComplaintResponse appealResponse = new ComplaintResponse();

            appealResponse.setUuid(appealData.getUuid());
            appealResponse.setReference(appealData.getReference());
            appealResponse.setReferenceNumber(appealData.getReferenceNumber());
            appealResponse.setOrganisationId(appealData.getOrganisationId());
            appealResponse.setDepartment(appealData.getDepartment());
            appealResponse.setTypeOfComplaint(appealData.getTypeOfComplaint());
            appealResponse.setState(appealData.getState());
            appealResponse.setStatus(appealData.getStatus());
            appealResponse.setCreatedAt(appealData.getCreatedAt());
            appealResponse.setUpdatedAt(appealData.getUpdatedAt());
            appealResponse.setCreatedBy(appealData.getCreatedBy());
            appealResponse.setUpdatedBy(appealData.getUpdatedBy());
            appealResponse.setAssignedTo(appealData.getAssignee());
            appealResponse.setAssignedBy(appealData.getAssigneeRole() != null ? appealData.getAssigneeRole().name() : "MANAGER");
            appealResponse.setProcessInstanceId(appealData.getProcessInstanceId());
            appealResponse.setDocuments(appealData.getDocuments());
            appealResponse.setComments(appealData.getComments());
            appealResponse.setAuditLogs(appealData.getAuditLogs());
            appealResponse.setDescription(appealData.getDescription());

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(appealData.getOrganisationId())).toList();

            if(!company.isEmpty()){
                appealResponse.setOrganisation(company.get(0).get("name").toString());
            }

            // Add original application information using the correct reference number
            ApplicationRejectionInfo originalApplication = null;
            if (appealData.getReference() != null && !appealData.getReference().trim().isEmpty()) {
                originalApplication = getOriginalApplicationInfo(appealData.getTypeOfComplaint(), appealData.getReference());
            } else {
                logger.warn("No original application reference found for appeal {}", appealId);
            }
            
            // Set original application info or default values if not found
            if (originalApplication != null) {
                appealResponse.setOriginalApplication(originalApplication);
            } else {
                // Set default original application info when not found
                ApplicationRejectionInfo defaultInfo = ApplicationRejectionInfo.builder()
                        .typeOfApplication(appealData.getTypeOfComplaint())
                        .status("UNKNOWN")
                        .rejectedBy("UNKNOWN")
                        .rejectedBy_userid("UNKNOWN")
                        .build();
                appealResponse.setOriginalApplication(defaultInfo);
            }

            return appealResponse;
        }
        return null;
    }

    /**
     * Gets original application information for displaying in appeal details
     * @param typeOfComplaint The type of complaint (e.g., "PRE_APPROVAL")
     * @param referenceNumber The reference number to lookup
     * @return ApplicationRejectionInfo with original application details, or null if not found
     */
    private ApplicationRejectionInfo getOriginalApplicationInfo(String typeOfComplaint, String referenceNumber) {
        logger.info("Fetching original application info for type {} and reference {}", typeOfComplaint, referenceNumber);
        
        if (referenceNumber == null || referenceNumber.trim().isEmpty()) {
            logger.warn("Reference number is null or empty, cannot fetch original application info");
            return null;
        }
        
        try {
            ApplicationRejectionInfo info = fetchApplicationInfo(typeOfComplaint, referenceNumber, false);
            if (info == null) {
                logger.warn("No application info found for reference: {}", referenceNumber);
            }
            return info;
        } catch (Exception e) {
            logger.error("Error fetching original application info for reference {}: {}", referenceNumber, e.getMessage());
            return null;
        }
    }

    /**
     * Common method to fetch application information from different repositories
     * @param typeOfComplaint The type of complaint
     * @param referenceNumber The reference number to lookup
     * @param validateRejected Whether to validate that the application is rejected
     * @return ApplicationRejectionInfo with application details
     */
    private ApplicationRejectionInfo fetchApplicationInfo(String typeOfComplaint, String referenceNumber, boolean validateRejected) {
        String applicationType = typeOfComplaint;
        ApplicationRejectionInfo rejectionInfo = null;
        
        try {
            if (applicationType.contains(Enums.ApplicationType.PRE_APPROVAL.name())) {
                // Check Pre-approval application
                Optional<PreApprovalApplication> application = preApprovalRepository.findByReferenceNumber(referenceNumber);
                
                if (application.isPresent()) {
                    PreApprovalApplication app = application.get();
                    
                    // Validate rejection status if required
                    if (validateRejected && !Enums.Status.REJECTED.name().equalsIgnoreCase(app.getStatus())) {
                        throw new InvalidReferenceException("Only rejected applications can be appealed. Current status: " + app.getStatus());
                    }
                    
                    // Determine who rejected the application based on assignment fields
                    String rejectionRole = determineRejectionRole(app.getAssignedAgent(), app.getAssignedOfficer(), app.getAssignedManager());
                    String rejectedUserId = getRejectedById(app.getAssignedAgent(), app.getAssignedOfficer(), app.getAssignedManager());
                    
                    // Build rejection info
                    rejectionInfo = ApplicationRejectionInfo.builder()
                            .typeOfApplication("PRE_APPROVAL")
                            .status(app.getStatus())
                            .rejectedBy(rejectionRole)
                            .rejectedBy_userid(rejectedUserId)
                            .build();
                    
                    logger.info("Found Pre-approval application: {}", referenceNumber);
                } else {
                    throw new InvalidReferenceException("Pre-approval application with reference " + referenceNumber + " not found");
                }
                
            } else if (applicationType.contains(Enums.ApplicationType.RECOGNITION.name())) {
                // Check NCBSC application
                Optional<NCBSCApplication> application = ncbscRepository.findByReferenceNumber(referenceNumber);
                
                if (application.isPresent()) {
                    NCBSCApplication app = application.get();
                    
                    // Validate rejection status if required
                    if (validateRejected && app.getApplicationStatus() != Enums.Status.REJECTED) {
                        throw new InvalidReferenceException("Only rejected applications can be appealed. Current status: " + app.getApplicationStatus());
                    }
                    
                    // Determine who rejected the application based on assignment fields
                    String rejectionRole = determineRejectionRole(app.getAssignedAgent(), app.getAssignedOfficer(), app.getAssignedManager());
                    String rejectedUserId = getRejectedById(app.getAssignedAgent(), app.getAssignedOfficer(), app.getAssignedManager());
                    
                    // Build rejection info
                    rejectionInfo = ApplicationRejectionInfo.builder()
                            .typeOfApplication("RECOGNITION")
                            .status(app.getApplicationStatus() != null ? app.getApplicationStatus().name() : "UNKNOWN")
                            .rejectedBy(rejectionRole)
                            .rejectedBy_userid(rejectedUserId)
                            .build();
                    
                    logger.info("Found NCBSC application: {}", referenceNumber);
                } else {
                    throw new InvalidReferenceException("NCBSC application with reference " + referenceNumber + " not found");
                }
                
            } else if (applicationType.contains(Enums.ApplicationType.NOC.name())) {
                // Check NOC application
                Optional<NOCApplication> application = nocRepository.findByReferenceNumber(referenceNumber);
                
                if (application.isPresent()) {
                    NOCApplication app = application.get();
                    
                    // Validate rejection status if required
                    if (validateRejected && app.getApplicationStatus() != Enums.Status.REJECTED) {
                        throw new InvalidReferenceException("Only rejected applications can be appealed. Current status: " + app.getApplicationStatus());
                    }
                    
                    // Determine who rejected the application based on assignment fields
                    String rejectionRole = determineRejectionRole(app.getAssignedAgent(), app.getAssignedOfficer(), app.getAssignedManager());
                    String rejectedUserId = getRejectedById(app.getAssignedAgent(), app.getAssignedOfficer(), app.getAssignedManager());
                    
                    // Build rejection info
                    rejectionInfo = ApplicationRejectionInfo.builder()
                            .typeOfApplication("NOC")
                            .status(app.getApplicationStatus() != null ? app.getApplicationStatus().name() : "UNKNOWN")
                            .rejectedBy(rejectionRole)
                            .rejectedBy_userid(rejectedUserId)
                            .build();
                    
                    logger.info("Found NOC application: {}", referenceNumber);
                } else {
                    throw new InvalidReferenceException("NOC application with reference " + referenceNumber + " not found");
                }
                
            } else if (applicationType.contains(Enums.ApplicationType.WORK_SKILLS.name())) {
                // Check Work Skill Training Plan application
                Optional<WorkPlaceTrainingPlan> application = workSkillRepository.findByReferenceNumber(referenceNumber);
                
                if (application.isPresent()) {
                    WorkPlaceTrainingPlan app = application.get();
                    
                    // Validate rejection status if required
                    if (validateRejected && !Enums.Status.REJECTED.name().equalsIgnoreCase(app.getApplicationStatus())) {
                        throw new InvalidReferenceException("Only rejected applications can be appealed. Current status: " + app.getApplicationStatus());
                    }
                    
                    // Determine who rejected the application based on assignment fields
                    String rejectionRole = determineRejectionRole(app.getAssignedAgent(), app.getAssignedOfficer(), app.getAssignedManager());
                    String rejectedUserId = getRejectedById(app.getAssignedAgent(), app.getAssignedOfficer(), app.getAssignedManager());
                    
                    // Build rejection info
                    rejectionInfo = ApplicationRejectionInfo.builder()
                            .typeOfApplication("WORK_SKILLS")
                            .status(app.getApplicationStatus())
                            .rejectedBy(rejectionRole)
                            .rejectedBy_userid(rejectedUserId)
                            .build();
                    
                    logger.info("Found Work Skills application: {}", referenceNumber);
                } else {
                    throw new InvalidReferenceException("Work Skills application with reference " + referenceNumber + " not found");
                }
                
            } else {
                throw new InvalidReferenceException("Unsupported application type: " + applicationType);
            }
            
        } catch (InvalidReferenceException e) {
            // Re-throw InvalidReferenceException as-is for validation scenarios
            if (validateRejected) {
                throw e;
            } else {
                // For non-validation scenarios (like fetching for display), log warning and return null
                logger.warn("Application not found for reference {}: {}", referenceNumber, e.getMessage());
                return null;
            }
        } catch (Exception e) {
            logger.error("Error fetching application info for reference {}: {}", referenceNumber, e.getMessage());
            if (validateRejected) {
                throw new InvalidReferenceException("Error fetching application info: " + e.getMessage());
            } else {
                // For non-validation scenarios, log error and return null
                logger.error("Unexpected error fetching application info for reference {}", referenceNumber, e);
                return null;
            }
        }
        
        return rejectionInfo;
    }

    public Page<ListData> fetchList(PageRequest pageable) {
        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }

        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and(BaseSpecifications.isAppeal());

        // Create a new PageRequest with sorting by createdAt in descending order (most recent first)
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
        PageRequest sortedPageable = PageRequest.of(
            pageable.getPageNumber(), 
            pageable.getPageSize(), 
            sort
        );

        Page<ComplaintEntity> appeals = appealRepository.findAll(spec, sortedPageable);
        return appeals.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment() != null ? app.getDepartment().name() : "UNKNOWN");
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState() != null ? app.getState().name() : "UNKNOWN");
            model.setStatus(app.getStatus() != null ? app.getStatus().name() : "UNKNOWN");

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintEntity updateStatus(String appealId, ComplaintState action, String userId) {
        ComplaintEntity appeal = fetchById(appealId);
        if(appeal != null){
            if (action==Enums.ComplaintState.REJECTED) {
                // appeal.setStatus(Enums.ComplaintStatus.CLOSED);
                appeal.setState(Enums.ComplaintState.REJECTED);
                appeal.setRejectionReason("Appeal rejected by manager");
                appeal.setRejectedAt(LocalDateTime.now());
                appeal.setRejectedBy(userId);
                appealRepository.save(appeal);
            } else if (action==Enums.ComplaintState.COMPLETED) {
                appeal.setStatus(Enums.ComplaintStatus.CLOSED);
                appeal.setState(Enums.ComplaintState.COMPLETED);
                appealRepository.save(appeal);
            }
           
            AuditLog log = new AuditLog(appeal, "STATUS_CHANGED", "Status changed to " + (action != null ? action.name() : "UNKNOWN"), userId, LocalDateTime.now());
            auditLogRepository.save(log);
        }

        return appeal;
    }

    @Transactional()
    public void createAppealDocument(ComplaintDocument appealDocument) {
        documentRepository.save(appealDocument);
    }

    private void validateWorkflowTransition(Enums.ComplaintStatus currentStatus, Enums.ComplaintState currentState,
                                            Enums.ComplaintStatus newStatus, Enums.ComplaintState newState) {

        if (currentStatus == Enums.ComplaintStatus.OPEN && currentState == Enums.ComplaintState.SUBMITTED) {
            if (newStatus == Enums.ComplaintStatus.IN_PROGRESS && newState == Enums.ComplaintState.UNDER_REVIEW) return;
           
            if (newStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && newState == Enums.ComplaintState.UNDER_REVIEW) return;
            
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return;
        }

        
        if (currentStatus == Enums.ComplaintStatus.IN_PROGRESS && currentState == Enums.ComplaintState.UNDER_REVIEW) {
            if (newStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && newState == Enums.ComplaintState.UNDER_REVIEW) return;
           
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return;
        }

       
        if (currentStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && currentState == Enums.ComplaintState.UNDER_REVIEW) {
            if (newStatus == Enums.ComplaintStatus.IN_PROGRESS && newState == Enums.ComplaintState.UNDER_REVIEW) return;
            
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return;
        }

        throw new IllegalArgumentException("Invalid status/state transition from " + currentStatus + "/" + currentState + " to " + newStatus + "/" + newState);
    }
    
    /**
     * Updates the original application status based on appeal decision
     * @param appeal The appeal entity containing reference to original application
     * @param action The action taken (APPROVE or REJECT)
     * @return true if update was successful, false otherwise
     */
    @Transactional
    public boolean updateOriginalApplicationStatus(ComplaintEntity appeal, String action) {
        if (appeal == null || appeal.getReference() == null || appeal.getReference().trim().isEmpty()) {
            logger.warn("Cannot update original application: Appeal or reference is null");
            return false;
        }
        
        String referenceNumber = appeal.getReference();
        String applicationType = appeal.getTypeOfComplaint();
        
        try {
            logger.info("Updating original application {} with status based on appeal action: {}", 
                    referenceNumber, action);
            
            if (ComplaintState.COMPLETED.name().equalsIgnoreCase(action)){
                // If appeal is approved, set application to PENDING/SUBMITTED
                if (applicationType.contains(Enums.ApplicationType.PRE_APPROVAL.name())) {
                    Optional<PreApprovalApplication> application = preApprovalRepository.findByReferenceNumber(referenceNumber);
                    if (application.isPresent()) {
                        PreApprovalApplication app = application.get();
                        app.setStatus(Enums.Status.PENDING.name());
                        app.setState(Enums.State.SUBMITTED.name());
                        app.setProcessInstanceId(null);
                        app.setAssignedAgent(null);
                        app.setAssignedAgentLead(null);
                        app.setAssignedOfficer(null);
                        app.setAssignedOfficerLead(null);
                        app.setAssignedManager(null);
                        PreApprovalApplication savedApp = preApprovalRepository.save(app);
                        
                        // Trigger workflow for the resubmitted application
                        try {
                            logger.info("Triggering workflow for resubmitted Pre-approval application: {}", referenceNumber);
                            Map<String, Object> workflowResponse = workflowClient.getNCBSCApplication(
                                Enums.ApplicationType.PRE_APPROVAL.name(), 
                                savedApp.getId().toString()
                            );
                            
                            if (workflowResponse != null && workflowResponse.containsKey("processInstanceId")) {
                                String processInstanceId = (String) workflowResponse.get("processInstanceId");
                                savedApp.setProcessInstanceId(processInstanceId);
                                preApprovalRepository.save(savedApp);
                                logger.info("Workflow started for Pre-approval application {}, process instance ID: {}", 
                                    referenceNumber, processInstanceId);
                            } else {
                                logger.warn("Failed to get process instance ID for Pre-approval application: {}", referenceNumber);
                            }
                        } catch (Exception e) {
                            logger.error("Error triggering workflow for Pre-approval application {}: {}", 
                                referenceNumber, e.getMessage(), e);
                            // Continue even if workflow triggering fails - the application is still reset
                        }
                        
                        logger.info("Updated Pre-approval application {} status to PENDING and cleared assignees", referenceNumber);
                        return true;
                    }
                } else if (applicationType.contains(Enums.ApplicationType.RECOGNITION.name()) ) {
                    Optional<NCBSCApplication> application = ncbscRepository.findByReferenceNumber(referenceNumber);
                    if (application.isPresent()) {
                        NCBSCApplication app = application.get();
                        app.setApplicationStatus(Enums.Status.PENDING);
                        app.setApplicationState(Enums.State.SUBMITTED);
                        app.setProcessInstanceId(null);
                        app.setAssignedAgent(null);
                        app.setAssignedAgentLead(null);
                        app.setAssignedOfficer(null);
                        app.setAssignedOfficerLead(null);
                        app.setAssignedManager(null);
                        NCBSCApplication savedApp = ncbscRepository.save(app);
                        
                        // Trigger workflow for the resubmitted application
                        try {
                            logger.info("Triggering workflow for resubmitted NCBSC application: {}", referenceNumber);
                            Map<String, Object> workflowResponse = workflowClient.getNCBSCApplication(
                                Enums.ApplicationType.RECOGNITION.name(), 
                                referenceNumber
                            );
                            
                            if (workflowResponse != null && workflowResponse.containsKey("processInstanceId")) {
                                String processInstanceId = (String) workflowResponse.get("processInstanceId");
                                savedApp.setProcessInstanceId(processInstanceId);
                                ncbscRepository.save(savedApp);
                                logger.info("Workflow started for NCBSC application {}, process instance ID: {}", 
                                    referenceNumber, processInstanceId);
                            } else {
                                logger.warn("Failed to get process instance ID for NCBSC application: {}", referenceNumber);
                            }
                        } catch (Exception e) {
                            logger.error("Error triggering workflow for NCBSC application {}: {}", 
                                referenceNumber, e.getMessage(), e);
                            // Continue even if workflow triggering fails - the application is still reset
                        }
                        
                        logger.info("Updated NCBSC application {} status to PENDING and cleared assignees", referenceNumber);
                        return true;
                    }
                } else if (applicationType.contains(Enums.ApplicationType.NOC.name())) {
                    Optional<NOCApplication> application = nocRepository.findByReferenceNumber(referenceNumber);
                    if (application.isPresent()) {
                        NOCApplication app = application.get();
                        app.setApplicationStatus(Enums.Status.PENDING);
                        app.setApplicationState(Enums.State.SUBMITTED);
                        app.setProcessInstanceId(null);
                        app.setAssignedAgent(null);
                        app.setAssignedAgentLead(null);
                        app.setAssignedOfficer(null);
                        app.setAssignedOfficerLead(null);
                        app.setAssignedManager(null);
                        NOCApplication savedApp = nocRepository.save(app);
                        
                        // Trigger workflow for the resubmitted application
                        try {
                            logger.info("Triggering workflow for resubmitted NOC application: {}", referenceNumber);
                            Map<String, Object> workflowResponse = workflowClient.getNCBSCApplication(
                                Enums.ApplicationType.NOC.name(), 
                                referenceNumber
                            );
                            
                            if (workflowResponse != null && workflowResponse.containsKey("processInstanceId")) {
                                String processInstanceId = (String) workflowResponse.get("processInstanceId");
                                savedApp.setProcessInstanceId(processInstanceId);
                                nocRepository.save(savedApp);
                                logger.info("Workflow started for NOC application {}, process instance ID: {}", 
                                    referenceNumber, processInstanceId);
                            } else {
                                logger.warn("Failed to get process instance ID for NOC application: {}", referenceNumber);
                            }
                        } catch (Exception e) {
                            logger.error("Error triggering workflow for NOC application {}: {}", 
                                referenceNumber, e.getMessage(), e);
                            // Continue even if workflow triggering fails - the application is still reset
                        }
                        
                        logger.info("Updated NOC application {} status to PENDING and cleared assignees", referenceNumber);
                        return true;
                    }
                } else if (applicationType.contains(Enums.ApplicationType.WORK_SKILLS.name()) ) {
                    Optional<WorkPlaceTrainingPlan> application = workSkillRepository.findByReferenceNumber(referenceNumber);
                    if (application.isPresent()) {
                        WorkPlaceTrainingPlan app = application.get();
                        app.setApplicationStatus(Enums.Status.PENDING.name());
                        app.setApplicationState(Enums.State.SUBMITTED.name());
                        app.setProcessInstanceId(null);
                        app.setAssignedAgent(null);
                        app.setAssignedAgentLead(null);
                        app.setAssignedOfficer(null);
                        app.setAssignedOfficerLead(null);
                        app.setAssignedManager(null);
                        WorkPlaceTrainingPlan savedApp = workSkillRepository.save(app);
                        
                        // Trigger workflow for the resubmitted application
                        try {
                            logger.info("Triggering workflow for resubmitted Work Skills application: {}", referenceNumber);
                            Map<String, Object> workflowResponse = workflowClient.getNCBSCApplication(
                                Enums.ApplicationType.WORK_SKILLS.name(), 
                                savedApp.getId().toString()
                            );
                            
                            if (workflowResponse != null && workflowResponse.containsKey("processInstanceId")) {
                                String processInstanceId = (String) workflowResponse.get("processInstanceId");
                                savedApp.setProcessInstanceId(processInstanceId);
                                workSkillRepository.save(savedApp);
                                logger.info("Workflow started for Work Skills application {}, process instance ID: {}", 
                                    referenceNumber, processInstanceId);
                            } else {
                                logger.warn("Failed to get process instance ID for Work Skills application: {}", referenceNumber);
                            }
                        } catch (Exception e) {
                            logger.error("Error triggering workflow for Work Skills application {}: {}", 
                                referenceNumber, e.getMessage(), e);
                            // Continue even if workflow triggering fails - the application is still reset
                        }
                        
                        logger.info("Updated Work Skill Training Plan {} status to PENDING and cleared assignees", referenceNumber);
                        return true;
                    }
                }
            } else if (ComplaintState.REJECTED.name().equalsIgnoreCase(action)) {
                // If appeal is rejected, application remains REJECTED (no change needed)
                logger.info("Appeal rejected, original application {} remains in REJECTED status", referenceNumber);
                return true;
            } else {
                logger.warn("Unknown appeal action: {}", action);
                return false;
            }
            
            logger.warn("Original application not found or type not supported: {}", referenceNumber);
            return false;
        } catch (Exception e) {
            logger.error("Error updating original application status: {}", e.getMessage(), e);
            return false;
        }
    }

    public Page<ListData> fetchCompanyList(String companyId, Pageable pageable) {
        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }
        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and(BaseSpecifications.isAppeal());
        spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("organisationId"), companyId));

        // Create a new PageRequest with sorting by createdAt in descending order (most recent first)
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
        PageRequest sortedPageable = PageRequest.of(
            pageable.getPageNumber(), 
            pageable.getPageSize(), 
            sort
        );

        Page<ComplaintEntity> appeals =  appealRepository.findAll(spec, sortedPageable);
        return appeals.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment() != null ? app.getDepartment().name() : "UNKNOWN");
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState() != null ? app.getState().name() : "UNKNOWN");
            model.setStatus(app.getStatus() != null ? app.getStatus().name() : "UNKNOWN");

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintsStats getCompanyStatistics(String companyId) {
        List<Object[]> results = appealRepository.getCompanyComplaintStatistics(companyId, Enums.CategoryComplaint.APPEAL.name());
        if (!results.isEmpty()) {
            Object[] row = results.get(0);
            return new ComplaintsStats(
                    ((Number) row[0]).longValue(),
                    ((Number) row[1]).longValue(),
                    ((Number) row[2]).longValue(),
                    ((Number) row[3]).longValue(),
                    ((Number) row[4]).longValue()
            );
        }
        return new ComplaintsStats(0L, 0L, 0L, 0L, 0L);
    }

    public ComplaintsStats getStatistics() {
        List<Object[]> results = appealRepository.getComplaintStatistics(Enums.CategoryComplaint.APPEAL.name());
        if (!results.isEmpty()) {
            Object[] row = results.get(0);
            return new ComplaintsStats(
                    ((Number) row[0]).longValue(),
                    ((Number) row[1]).longValue(),
                    ((Number) row[2]).longValue(),
                    ((Number) row[3]).longValue(),
                    ((Number) row[4]).longValue()
            );
        }
        return new ComplaintsStats(0L, 0L, 0L, 0L, 0L);
    }

    public Page<ListData> fetchUserAssignedList(PageRequest pageable, ComplaintSearchCriteria searchCriteria) {

        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }
        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and(BaseSpecifications.isAppeal());

        //  ADD: Role-based filtering (Same as complaint module)
        if (searchCriteria.getRole() != null) {
            spec = spec.and(buildAppealRoleBasedSpecification(searchCriteria.getRole(), searchCriteria.getAssignedTo()));
        } else if (searchCriteria.getAssignedTo() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("assignee"), searchCriteria.getAssignedTo()));
        }

        //  ADD: Status filtering
        if (searchCriteria.getStatus() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("status"), Enums.ComplaintStatus.valueOf(searchCriteria.getStatus().toUpperCase())));
        }

        //  ADD: State filtering
        if (searchCriteria.getState() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("state"), Enums.ComplaintState.valueOf(searchCriteria.getState().toUpperCase())));
        }

        //  ADD: Reference number filtering
        if (searchCriteria.getReferenceNumber() != null && !searchCriteria.getReferenceNumber().isEmpty()) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("referenceNumber")),
                    "%" + searchCriteria.getReferenceNumber().toLowerCase() + "%"));
        }

        //  ADD: Department filtering
        if (searchCriteria.getDepartment() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("department"), Enums.Department.valueOf(searchCriteria.getDepartment().toUpperCase())));
        }

        //  ADD: Date range filtering
        if (searchCriteria.getStartDate() != null && searchCriteria.getEndDate() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.between(
                    root.get("createdAt"), searchCriteria.getStartDate(), searchCriteria.getEndDate()));
        }

        if (searchCriteria.getComplaintStates() != null && !searchCriteria.getComplaintStates().isEmpty()) {
            List<Enums.ComplaintState> states = searchCriteria.getComplaintStates();
            spec = spec.and((root, query, criteriaBuilder) -> root.get("state").in(states));
        }
        

        Page<ComplaintEntity> appeals = appealRepository.findAll(spec, pageable);
        return appeals.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment() != null ? app.getDepartment().name() : "UNKNOWN");
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState() != null ? app.getState().name() : "UNKNOWN");
            model.setStatus(app.getStatus() != null ? app.getStatus().name() : "UNKNOWN");

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintEntity assignedNewAssistance(String appealId, String  agentId, String userId) {
        ComplaintEntity appeal = fetchById(appealId);
        if(appeal != null){
            appeal.setAssignee(agentId);
            appealRepository.save(appeal);

            AuditLog log = new AuditLog(appeal, "ASSIGN_AGENT_TO_COMPLAINT", "Appeal assigned to human agent ", userId, LocalDateTime.now());
            auditLogRepository.save(log);
        }


        return appeal;
    }

    /**
     *  Build enhanced appeal creation response with rejection information
     */
    private AppealCreationResponse buildAppealCreationResponse(ComplaintEntity savedAppeal, ApplicationRejectionInfo rejectionInfo) {
        AppealCreationResponse response = new AppealCreationResponse();

        // Basic appeal information
        response.setUuid(savedAppeal.getUuid());
        response.setReferenceNumber(savedAppeal.getReferenceNumber());
        response.setReference(savedAppeal.getReference());
        response.setDepartment(savedAppeal.getDepartment());
        response.setTypeOfComplaint(savedAppeal.getTypeOfComplaint());
        response.setStatus(savedAppeal.getStatus());
        response.setState(savedAppeal.getState());
        response.setAssignee(savedAppeal.getAssignee());
        response.setCreatedAt(savedAppeal.getCreatedAt());
        response.setOrganisationId(savedAppeal.getOrganisationId());
        response.setRejectedBy(savedAppeal.getRejectedBy());
        
        // Set original application information
        response.setOriginalApplication(rejectionInfo);

        return response;
    }

    /**
     *  Build basic appeal creation response (for backwards compatibility)
     */
    // private AppealCreationResponse buildAppealCreationResponse(ComplaintEntity savedAppeal) {
    //     return buildAppealCreationResponse(savedAppeal, null);
    // }

    /**
     *  FIXED: Role-based filtering for appeals - Only managers should handle appeals
     */
    private Specification<ComplaintEntity> buildAppealRoleBasedSpecification(String role, String userId) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            logger.info("Building appeal role-based specification for role: {} and userId: {}", role, userId);

            switch (role.toUpperCase()) {
                case "MANAGER":
                    
                    if (userId != null && !userId.isEmpty()) {
                        predicates.add(criteriaBuilder.equal(root.get("assignee"), userId));
                        logger.info("MANAGER filter (with userId): assignee={}", userId);
                    } else {
                        // If no userId provided, managers see all appeals
                        logger.info("MANAGER filter (without userId): all appeals");
                    }
                    break;

                case "AGENT_LEAD":
                    
                    predicates.add(criteriaBuilder.equal(root.get("uuid"), "NO_MATCH"));
                    logger.info("AGENT_LEAD filter: No access to appeals");
                    break;

                case "AGENT":
                    
                    predicates.add(criteriaBuilder.equal(root.get("uuid"), "NO_MATCH"));
                    logger.info("AGENT filter: No access to appeals");
                    break;

                default:
                    
                    predicates.add(criteriaBuilder.equal(root.get("uuid"), "NO_MATCH"));
                    logger.info("Unknown role filter: No access");
            }

            return predicates.isEmpty() ? criteriaBuilder.conjunction() :
                   criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

}
