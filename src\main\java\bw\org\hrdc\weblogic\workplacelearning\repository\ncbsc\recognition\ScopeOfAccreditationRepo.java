package bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ScopeOfAccreditation;
import lombok.NonNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 22/02/25 11:08
 * @UpdatedBy martinspectre
 * @UpdatedOn 22/02/25 11:08
 */
@Repository
public interface ScopeOfAccreditationRepo extends JpaRepository<ScopeOfAccreditation, Long>, JpaSpecificationExecutor<ScopeOfAccreditation> {

    @Query(value = "SELECT * FROM scope_of_accreditation a WHERE a.application_id = :applicationId", nativeQuery = true)
    @NonNull
    List<ScopeOfAccreditation> findByApplicationId(@NonNull String applicationId);
}
