package bw.org.hrdc.weblogic.workplacelearning.mapper.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.RFCCourseDeliveryScheduleDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RFCCourseDeliverySchedule;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RequestForSignificantChanges;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class RFCCourseDeliveryScheduleMapper {

    public static RFCCourseDeliverySchedule toEntity(RFCCourseDeliveryScheduleDto dto, RequestForSignificantChanges application) {
        if (dto == null) {
            return null;
        }

        RFCCourseDeliverySchedule entity = new RFCCourseDeliverySchedule();
        entity.setApplication(application); // Set parent reference
        entity.setDate(dto.getDate());
        entity.setTopic(dto.getTopic());
        entity.setHours(dto.getHours());

        return entity;
    }

    public static RFCCourseDeliveryScheduleDto toDto(RFCCourseDeliverySchedule entity) {
        if (entity == null) {
            return null;
        }

        RFCCourseDeliveryScheduleDto dto = new RFCCourseDeliveryScheduleDto();
//        dto.setUuid(UUID.fromString(entity.getUuid()));
        dto.setDate(entity.getDate());
        dto.setTopic(entity.getTopic());
        dto.setHours(entity.getHours());

        return dto;
    }

    public static List<RFCCourseDeliverySchedule> toEntityList(List<RFCCourseDeliveryScheduleDto> dtos, RequestForSignificantChanges application) {
        if (dtos == null) {
            return null;
        }

        return dtos.stream()
                .map(dto -> toEntity(dto, application))
                .collect(Collectors.toList());
    }

//    public static List<RFCCourseDeliveryScheduleDto> toDtoList(List<RFCCourseDeliverySchedule> entities) {
//        if (entities == null) {
//            return null;
//        }
//
//        return entities.stream()
//                .map(RFCCourseDeliveryScheduleMapper::toDto)
//                .collect(Collectors.toList());
//    }

    public static List<RFCCourseDeliveryScheduleDto> toDtoList(List<RFCCourseDeliverySchedule> entities) {
        if (entities == null || entities.isEmpty()) {
            return Collections.emptyList(); // ✅ Prevents null from causing .stream() error
        }
        return entities.stream()
                .map(RFCCourseDeliveryScheduleMapper::toDto)
                .collect(Collectors.toList());
    }
}
