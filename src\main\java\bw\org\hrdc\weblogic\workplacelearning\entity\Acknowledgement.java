package bw.org.hrdc.weblogic.workplacelearning.entity;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.UUID;

@Entity
@Table(name = "acknowledgement")
@Getter
@Setter
@ToString
public class Acknowledgement {

    @Id
    @GeneratedValue
    private UUID id;

    @Column(nullable = false)
    private String reference_no;

    @Column(nullable = false)
    private Date date_generated;

    @Enumerated(EnumType.STRING)
    private ApplicationStatus acknowledgementStatus;

    @OneToOne
    @JoinColumn(name = "non_credit_bearing_course_id", nullable = false)
    private NonCreditBearingCourse nonCreditBearingCourse;
}
