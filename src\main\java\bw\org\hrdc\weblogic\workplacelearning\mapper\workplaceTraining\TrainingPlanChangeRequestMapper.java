package bw.org.hrdc.weblogic.workplacelearning.mapper.workplaceTraining;

import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.WorkPlaceTrainingPlanChangeRequestDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlanChangeRequest;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class TrainingPlanChangeRequestMapper {

    public static WorkPlaceTrainingPlanChangeRequest toEntity(WorkPlaceTrainingPlanChangeRequestDto dto) {
        if (dto == null) {
            return null;
        }

        WorkPlaceTrainingPlanChangeRequest entity = new WorkPlaceTrainingPlanChangeRequest();
        entity.setId(dto.getId() != null ? dto.getId() : System.nanoTime());
        entity.setUuid(dto.getUuid() != null ? dto.getUuid() : UUID.randomUUID().toString());

        entity.setOrganisationId(dto.getOrganisationId());
        entity.setApplicationStatus(dto.getApplicationStatus());
        entity.setApplicationState(dto.getApplicationState());
        entity.setApplicationNumber(dto.getApplicationNumber());
        entity.setReferenceNumber(dto.getReferenceNumber());
        entity.setFinancialYear(dto.getFinancialYear());
        entity.setLocation(dto.getLocation());
        entity.setContactDate(dto.getContactDate());
        entity.setSubmissionDate(dto.getSubmissionDate());
        entity.setCourseDetails(dto.getCourseDetails());

        return entity;
    }

}
