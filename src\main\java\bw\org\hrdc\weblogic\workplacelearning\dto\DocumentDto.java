package bw.org.hrdc.weblogic.workplacelearning.dto;

import bw.org.hrdc.weblogic.workplacelearning.constants.DocumentStatus;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for managing Document details.
 */
@Data
@Schema(
        name = "Document",
        description = "Schema to hold Document details"
)
public class DocumentDto {

    @Schema(description = "Unique identifier for the document", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @NotNull(message = "Name cannot be null")
    @Schema(description = "Name of the document", example = "Course Overview")
    private String name;

    @Schema(description = "Type of the document", example = "PDF")
    private Enums.DocumentType documentType;

    @NotNull(message = "File path cannot be null")
    @Schema(description = "Path where the document is stored", example = "/documents/course-overview.pdf")
    private String filePath;

    @Schema(description = "Status of the document", example = "ACTIVE")
    private DocumentStatus status;

    @NotNull(message = "Organisation ID cannot be null")
    @Schema(description = "The ID of the organization associated with the document", example = "c3d4f50b-58cc-4372-a567-0e02b2c3d234")
    private UUID organisationId;

    @Schema(description = "The ID of the associated Non-Credit Bearing Course", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID nonCreditBearingCourseId;

    @Schema(description = "Timestamp when the document was created", example = "2024-11-26T10:00:00Z")
    private LocalDateTime createdAt;

    @Schema(description = "User who created the document", example = "<EMAIL>")
    private String createdBy;

    @Schema(description = "Timestamp when the document was last updated", example = "2024-11-28T14:00:00Z")
    private LocalDateTime updatedAt;

    @Schema(description = "User who last updated the document", example = "<EMAIL>")
    private String updatedBy;
}
