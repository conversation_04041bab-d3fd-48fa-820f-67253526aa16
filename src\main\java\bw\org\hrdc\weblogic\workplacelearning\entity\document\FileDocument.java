package bw.org.hrdc.weblogic.workplacelearning.entity.document;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Auditable;
import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "file")
public class FileDocument {

    @Id
    @Column(nullable = false, updatable = false, unique = true)
    @JsonProperty(value="id")
    private UUID id;

    @Column(name = "doc_key", nullable = false)
    private String key;

    @Column(name = "doc_name", nullable = false)
    private String docName;

    @Column(name = "doc_ext", nullable = false)
    private String docExt;

    @Column(name = "identifier", nullable = false)
    private String identifier;

    @Column(name = "file_type", nullable = false)
    private String fileType;

    @Column(name = "doc_size", nullable = false)
    private Long docSize;

    @Column(name = "input_identifier", nullable = false)
    private String inputIdentifier;

    @Column(name = "file_url", nullable = false, columnDefinition = "TEXT")
    private String fileUrl;

    @Enumerated(EnumType.STRING)
    @Column(name = "entity_type", nullable = false)
    private Enums.FileEntityType entityType;

    @Column(name = "entity_id", nullable = false)
    private UUID entityId;

    @PrePersist
    protected void onCreate() {
        this.id = UUID.randomUUID();
    }
}
