package bw.org.hrdc.weblogic.workplacelearning.mapper.workplaceTraining;

import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.WorkPlaceTrainingPlanDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.CourseDetails;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.time.LocalDateTime;

@Component
public class WorkPlaceTrainingPlanMapper {

    public static WorkPlaceTrainingPlanDto toDto(WorkPlaceTrainingPlan entity) {
        if (entity == null) {
            return null;
        }

        WorkPlaceTrainingPlanDto dto = new WorkPlaceTrainingPlanDto();
        dto.setId(entity.getId());
        dto.setUserId(entity.getUserId());
        dto.setOrganisationId(entity.getOrganisationId());
        dto.setApplicationStatus(entity.getApplicationStatus());
        dto.setApplicationState(entity.getApplicationState());
        dto.setApplicationNumber(entity.getApplicationNumber());
        dto.setReferenceNumber(entity.getReferenceNumber());
        dto.setFinancialYear(entity.getFinancialYear());
        dto.setLocation(entity.getLocation());
        dto.setContactDate(entity.getContactDate());
        dto.setSubmissionDate(entity.getSubmissionDate());
        dto.setCreatedDate(entity.getCreatedDate());
        dto.setLastModifiedDate(entity.getLastModifiedDate());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setLastModifiedBy(entity.getLastModifiedBy());

        dto.setCourseDetails(entity.getCourseDetails() != null
                ? entity.getCourseDetails().stream()
                .map(CourseDetailsMapper::toDto)
                .collect(Collectors.toList())
                : Collections.emptyList());

        return dto;
    }

    public static WorkPlaceTrainingPlan toEntity(WorkPlaceTrainingPlanDto dto) {
        if (dto == null) {
            return null;
        }

        WorkPlaceTrainingPlan entity = new WorkPlaceTrainingPlan();
        entity.setId(dto.getId() != null ? dto.getId() : UUID.randomUUID());
        entity.setUserId(dto.getUserId());
        entity.setOrganisationId(dto.getOrganisationId());
        entity.setApplicationStatus(dto.getApplicationStatus());
        entity.setApplicationState(dto.getApplicationState());
        entity.setApplicationNumber(dto.getApplicationNumber());
        entity.setReferenceNumber(dto.getReferenceNumber());
        entity.setFinancialYear(dto.getFinancialYear());
        entity.setLocation(dto.getLocation());
        entity.setContactDate(dto.getContactDate());
        entity.setSubmissionDate(dto.getSubmissionDate());
        
        // Set created date and last modified date
        LocalDateTime now = LocalDateTime.now();
        entity.setCreatedDate(now);
        entity.setLastModifiedDate(now);

        if (dto.getCourseDetails() != null) {
            List<CourseDetails> courses = dto.getCourseDetails().stream()
                    .map(courseDto -> CourseDetailsMapper.toEntity(courseDto, entity))
                    .collect(Collectors.toList());
            entity.setCourseDetails(courses);
        }

        return entity;
    }

    public Page<WorkPlaceTrainingPlanDto> toDtoPage(Page<WorkPlaceTrainingPlan> trainingPlans) {
        return trainingPlans.map(WorkPlaceTrainingPlanMapper::toDto);
    }

}
