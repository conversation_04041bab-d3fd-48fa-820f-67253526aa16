package bw.org.hrdc.weblogic.workplacelearning.mapper.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.RFCCourseContentAndDeliveryDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RFCCourseContentAndDelivery;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RequestForSignificantChanges;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
public class RFCCourseContentAndDeliveryMapper {

    public static RFCCourseContentAndDeliveryDto toDto(RFCCourseContentAndDelivery entity) {
        if (entity == null) {
            return null;
        }

        RFCCourseContentAndDeliveryDto dto = new RFCCourseContentAndDeliveryDto();
        dto.setUuid(UUID.fromString(entity.getUuid()));
        dto.setExitLevelOutcomes(entity.getExitLevelOutcomes());
        dto.setLearningOutcomes(entity.getLearningOutcomes());
        dto.setShortCourseDeliveryMode(entity.getShortCourseDeliveryMode());
        dto.setLearningOutcomesSummary(entity.getLearningOutcomesSummary());
        dto.setLocation(entity.getLocation());
        dto.setShortCourseDeliveryType(entity.getShortCourseDeliveryType());

        return dto;
    }

    public static RFCCourseContentAndDelivery toEntity(RFCCourseContentAndDeliveryDto dto, RequestForSignificantChanges application) {
        if (dto == null) {
            return null;
        }

        RFCCourseContentAndDelivery entity = new RFCCourseContentAndDelivery();
//        entity.setUuid(String.valueOf(dto.getUuid()));
        entity.setApplication(application); // Set parent reference
        entity.setExitLevelOutcomes(dto.getExitLevelOutcomes());
        entity.setLearningOutcomes(dto.getLearningOutcomes());
        entity.setShortCourseDeliveryMode(dto.getShortCourseDeliveryMode());
        entity.setLearningOutcomesSummary(dto.getLearningOutcomesSummary());
        entity.setLocation(dto.getLocation());
        entity.setShortCourseDeliveryType(dto.getShortCourseDeliveryType());

        return entity;
    }

    public static List<RFCCourseContentAndDelivery> toEntityList(List<RFCCourseContentAndDeliveryDto> dtos, RequestForSignificantChanges application) {

        if (dtos == null) {
            return null;
        }
        return dtos.stream()
                .map(schedule -> RFCCourseContentAndDeliveryMapper.toEntity(schedule, application))
                .collect(Collectors.toList());
    }

    public static List<RFCCourseContentAndDeliveryDto> toDtoList(List<RFCCourseContentAndDelivery> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(RFCCourseContentAndDeliveryMapper::toDto)
                .collect(Collectors.toList());
    }
}