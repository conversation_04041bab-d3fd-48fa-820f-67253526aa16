package bw.org.hrdc.weblogic.workplacelearning.entity;


import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.UUID;

@Entity
@Table(name = "assessment_and_certification")
@Getter
@Setter
@ToString
public class AssessmentAndCertification {

    @Id
    @GeneratedValue
    private UUID id;

    @Column(nullable = false)
    private String certification_requirements;

    @Column(nullable = false)
    private String assessment_strategy;

    @ManyToOne
    @JoinColumn(name = "non_credit_bearing_course_id", nullable = false)
    private NonCreditBearingCourse nonCreditBearingCourse;
}
