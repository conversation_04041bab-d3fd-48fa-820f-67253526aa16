package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.UUID;

/**
 * DTO for managing Particular of Training details.
 */
@Data
@Schema(
        name = "ParticularOfTraining",
        description = "Schema to hold training details"
)
public class ParticularOfTrainingDto {

    @Schema(description = "Unique identifier for the training detail", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @Schema(description = "Indicates if training is product related", example = "true")
    private Boolean productRelatedTraining;

    @Schema(description = "Indicates if training is outsourced", example = "false")
    private Boolean outsourcedTraining;

    @Schema(description = "Indicates if pre-approval for non-citizen employee is required", example = "true")
    private Boolean nonCitizenEmployee;

    @Schema(description = "Indicates if the training is local", example = "true")
    private Boolean isTrainingLocal;
}
