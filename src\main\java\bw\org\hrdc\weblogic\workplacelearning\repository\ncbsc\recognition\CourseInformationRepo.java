package bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ShortCourseInformation;
import lombok.NonNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @CreatedOn 22/02/25 11:08
 * @UpdatedBy martinspectre
 * @UpdatedOn 22/02/25 11:08
 */
@Repository
public interface CourseInformationRepo extends JpaRepository<ShortCourseInformation, Long>, JpaSpecificationExecutor<ShortCourseInformation> {
    @Query("SELECT s FROM ShortCourseInformation s " +
            "JOIN s.application a " +
            "WHERE a.organisationId = :companyId")
    Page<ShortCourseInformation> findByCompany(Pageable pageable, @Param("companyId") String companyId);

    @Query("SELECT a FROM ShortCourseInformation a WHERE LOWER(a.uuid) = LOWER(:uuid)")
    @NonNull
    Optional<ShortCourseInformation> findById(@Param("uuid") String uuid);
}
