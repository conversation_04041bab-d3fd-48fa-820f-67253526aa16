package bw.org.hrdc.weblogic.workplacelearning.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Custom deserializer for handling JSON string format for List<String>.
 */
public class JsonStringListDeserializer extends JsonDeserializer<List<String>> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<String> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        String value = p.getValueAsString();
        if (value == null || value.isEmpty()) {
            return new ArrayList<>();
        }
        
        // If the value is already a JSON array, parse it directly
        if (value.startsWith("[") && value.endsWith("]")) {
            try {
                return objectMapper.readValue(value, List.class);
            } catch (Exception e) {
                // If parsing fails, return a list with the original string
                List<String> result = new ArrayList<>();
                result.add(value);
                return result;
            }
        }
        
        // If it's a single value, return a list with that value
        List<String> result = new ArrayList<>();
        result.add(value);
        return result;
    }
} 