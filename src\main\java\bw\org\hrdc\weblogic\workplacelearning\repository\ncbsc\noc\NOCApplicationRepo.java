package bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.noc;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.INCBSCApplicationQueryListDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import lombok.NonNull;
import org.hibernate.annotations.Fetch;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

/**
 * Repository for NOCApplication entity.
 */
@Repository
public interface NOCApplicationRepo extends JpaRepository<NOCApplication, UUID>, JpaSpecificationExecutor<NOCApplication>{
    @Query(value = "SELECT a.reference_number, a.organisation_id, a.short_course_delivery_mode, sci.title, a.date_submitted, " +
            "a.application_status, a.assigned_agent, a.assigned_agent_lead, a.assigned_officer_lead, a.assigned_officer, " +
            "a.assigned_manager FROM ncbsc_application a " +
            "INNER JOIN short_course_information sci ON sci.application_id = a.uuid " +
            "WHERE a.is_deleted = false",
            nativeQuery = true
    )
    Page<INCBSCApplicationQueryListDto> findAllApplications(@NonNull Pageable pageable);

    @Query("SELECT a FROM NOCApplication a WHERE LOWER(a.uuid) = LOWER(:id) AND a.isDeleted = false")
    @NonNull
    Optional<NOCApplication> findById(String id);

    @Modifying
    @Transactional
    @Query("UPDATE NOCApplication a SET a.isDeleted = true WHERE LOWER(a.uuid) = LOWER(:id)")
    int deleteById(@Param("id") String id);

    @Query("SELECT a FROM NOCApplication a WHERE LOWER(a.referenceNumber) = LOWER(:referenceNumber) AND a.isDeleted = false")
    @NonNull
    Optional<NOCApplication> findByReferenceNumber(@Param("referenceNumber") String referenceNumber);

    @Modifying
    @Query("UPDATE NOCApplication a SET " +
            "a.updatedAt = CURRENT_TIMESTAMP, " +
            "a.assignedAgent = CASE WHEN :role = 'AGENT' THEN :userId ELSE a.assignedAgent END, " +
            "a.assignedAgentLead = CASE WHEN :role = 'AGENT_LEAD' THEN :userId ELSE a.assignedAgentLead END, " +
            "a.assignedOfficer = CASE WHEN :role = 'OFFICER' THEN :userId ELSE a.assignedOfficer END, " +
            "a.assignedOfficerLead = CASE WHEN :role = 'OFFICER_LEAD' THEN :userId ELSE a.assignedOfficerLead END, " +
            "a.assignedManager = CASE WHEN :role = 'MANAGER' THEN :userId ELSE a.assignedManager END, " +
            "a.applicationState = " +
            "   CASE " +
            "       WHEN :role = 'AGENT' THEN 'IN_PROCESSING' " +
            "       WHEN :role = 'AGENT_LEAD' THEN 'SUBMITTED' " +
            "       WHEN :role = 'OFFICER' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'OFFICER_LEAD' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'MANAGER' THEN 'IN_APPROVAL' " +
            "       ELSE a.applicationState " +
            "   END, " +
            "a.applicationStatus = 'PENDING' " +
            "WHERE LOWER(a.referenceNumber) = LOWER(:referenceNumber) AND a.isDeleted = false" )
    int updateApplicationAssignedUser(
            @Param("referenceNumber") String referenceNumber,
            @Param("role") String role,
            @Param("userId") String userId);

    @Modifying
    @Query("UPDATE NOCApplication a " +
            "SET a.assignedOfficerLead = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN :newAssignee " +
            "    ELSE a.assignedOfficerLead " +
            "END, " +
            "a.applicationState = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN 'IN_REVIEW' " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN 'IN_APPROVAL' " +
            "    ELSE a.applicationState " +
            "END, " +
            "a.applicationStatus = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN 'PENDING' " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN 'PENDING' " +
            "    ELSE :action " +
            "END, " +
            "a.assignedManager = CASE " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN :newAssignee " +
            "    ELSE a.assignedManager " +
            "END, " +
            "a.assignedAgent = CASE " +
            "    WHEN :role = 'OFFICER' AND :action = 'CHANGE_REQUEST' THEN :newAssignee " +
            "    ELSE a.assignedAgent " +
            "END " +
            "WHERE LOWER(a.referenceNumber) = LOWER(:referenceNumber) AND a.isDeleted = false")
    int changeApplicationStatus(
            @Param("referenceNumber") String referenceNumber,
            @Param("role") String role,
            @Param("action") String action,
            @Param("newAssignee") String newAssignee);

    @Query("SELECT a FROM NOCApplication a WHERE a.organisationId = :companyId AND a.isDeleted = false")
    @NonNull
    Page<NOCApplication> findByCompanyId(@Param("companyId") String companyId, @NonNull Pageable pageable);

    @Query("SELECT a FROM NOCApplication a WHERE LOWER(a.applicationNumber) = LOWER(:applicationNumber)")
    @NonNull
    Optional<NOCApplication> fetchByApplicationNumber(@Param("applicationNumber") String applicationNumber);

    /**
     * Update the process instance ID using a native SQL query to bypass auditing.
     * This is used as a fallback when the JPA update fails due to auditing issues.
     */
    @Modifying
    @Transactional
    @Query(value = "UPDATE ncbsc_change_request SET process_instance_id = :processInstanceId, " +
            "updated_by = 'system-user', updated_at = CURRENT_TIMESTAMP " +
            "WHERE reference_number = :referenceNumber", 
            nativeQuery = true)
    int updateProcessInstanceIdNative(
            @Param("processInstanceId") String processInstanceId, 
            @Param("referenceNumber") String referenceNumber);
}
