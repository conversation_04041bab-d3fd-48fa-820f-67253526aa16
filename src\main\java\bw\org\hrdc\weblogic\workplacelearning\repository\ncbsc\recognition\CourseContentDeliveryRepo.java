package bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.CourseContentDelivery;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @CreatedOn 22/02/25 11:08
 * @UpdatedBy martinspectre
 * @UpdatedOn 22/02/25 11:08
 */
@Repository
public interface CourseContentDeliveryRepo extends JpaRepository<CourseContentDelivery, Long>, JpaSpecificationExecutor<CourseContentDelivery> {

    @Query("SELECT s FROM CourseContentDelivery s " +
            "JOIN s.application a " +
            "WHERE a.uuid = :applicationId")
    Optional<CourseContentDelivery> findByApplication(@Param("applicationId") String applicationId);

    @Query("SELECT s FROM CourseContentDelivery s " +
            "WHERE s.uuid = :uuid")
    Optional<CourseContentDelivery> findByUuid(@Param("uuid") String uuid);
}
