package bw.org.hrdc.weblogic.workplacelearning.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.UUID;

@Entity
@Table(name = "action_plan")
@Getter
@Setter
@ToString
public class ActionPlan {

    @Id
    @GeneratedValue
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "evaluation_id", nullable = false)
    private ETPEvaluation evaluation;

    @Column(name = "weight", nullable = false)
    private String weight;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "action_agent")
    private String actionAgent;

    @Temporal(TemporalType.DATE)
    @Column(name = "completion_date")
    private Date completionDate;
}

