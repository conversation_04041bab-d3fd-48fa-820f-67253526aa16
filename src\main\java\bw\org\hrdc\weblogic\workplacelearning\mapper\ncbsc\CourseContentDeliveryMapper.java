package bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.CourseContentDeliveryDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.CourseContentDelivery;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.LearningOutcome;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * <AUTHOR>
 * @CreatedOn 18/02/25 20:10
 * @UpdatedBy martinspectre
 * @UpdatedOn 18/02/25 20:10
 */
@Component
public class CourseContentDeliveryMapper {

    public static CourseContentDelivery toEntity(CourseContentDeliveryDto model, NCBSCApplication application) {
        if (model == null) {
            return null;
        }
        CourseContentDelivery courseContentDelivery =  new CourseContentDelivery();
        courseContentDelivery.setExitLevelOutcomes(model.getExitLevelOutcomes());
        courseContentDelivery.setLearningOutcomesSummary(model.getLearningOutcomesSummary());
        courseContentDelivery.setShortCourseDeliveryMode(model.getShortCourseDeliveryMode());
        courseContentDelivery.setApplication(application);
        courseContentDelivery.setLocation(model.getLocation());
        courseContentDelivery.setShortCourseDeliveryType(model.getShortCourseDeliveryType());

        return courseContentDelivery;
    }

    public static CourseContentDeliveryDto toDto(CourseContentDelivery entity, Set<LearningOutcome> learningOutcome) {
        if (entity == null) {
            return null;
        }
        CourseContentDeliveryDto model = new CourseContentDeliveryDto();
        model.setExitLevelOutcomes(entity.getExitLevelOutcomes());
        model.setLearningOutcomesSummary(entity.getLearningOutcomesSummary());
        model.setShortCourseDeliveryMode(entity.getShortCourseDeliveryMode());
        model.setLearningOutcomes(LearningOutcomeMapper.toDtoList(learningOutcome));
        model.setLocation(entity.getLocation());
        model.setShortCourseDeliveryType(entity.getShortCourseDeliveryType());

        return model;
    }
}
