# Use the official Maven image to build the project.
FROM maven:3.8.5-openjdk-17 AS build

# Set the working directory inside the container
WORKDIR /app

# Copy the pom.xml and download dependencies
COPY pom.xml /app/
RUN mvn dependency:go-offline

# Copy the rest of the project files
COPY . /app/

# Run Maven clean and install to build the project
RUN mvn clean install -DskipTests

# Use the official OpenJDK image to run the application
FROM openjdk:17-jdk-slim

# Set the working directory inside the container
WORKDIR /app

# Copy the built JAR file from the build stage
COPY --from=build /app/target/workplace-learning-0.0.1-SNAPSHOT.jar /app/workplace-learning.jar

EXPOSE 8091

# Run the application
ENTRYPOINT ["java", "-jar", "/app/workplace-learning.jar"]