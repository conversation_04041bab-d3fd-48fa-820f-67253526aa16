package bw.org.hrdc.weblogic.workplacelearning.service.impl.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.ShortCourseInformationListDto;
import bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc.ShortCourseRowMapper;
import bw.org.hrdc.weblogic.workplacelearning.service.IShortCourseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 26/03/25 16:42
 * @UpdatedBy martinspectre
 * @UpdatedOn 26/03/25 16:42
 */
@Repository
public class ShortCourseRepositoryImpl implements IShortCourseRepository {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<ShortCourseInformationListDto> findShortCoursesByOrganisationId(String organisationId) {
        String sql = "SELECT sc.uuid, sc.title, sc.type, sc.start_date, sc.end_date, " +
                "sc.level, sc.accrediting_body, scd.exit_level_outcomes " +
                "FROM public.ncbsc_application a " +
                "INNER JOIN short_course_information sc ON a.uuid = sc.application_id " +
                "INNER JOIN course_content_delivery scd ON a.uuid = scd.application_id " +
                "WHERE a.organisation_id = ?";
        return jdbcTemplate.query(sql, new Object[]{organisationId}, new ShortCourseRowMapper());
    }
}
