package bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @CreatedOn 18/02/25 20:05
 * @UpdatedBy martinspectre
 * @UpdatedOn 18/02/25 20:05
 */
@Data
public class NCBSCApplicationListDto {
    private String referenceNumber;
    private String applicationNumber;
    private String organisationId;
    private String companyName;
    private String shortCourseDeliveryMode;
    private String courseTitle;
    private String assignedTo;
    private LocalDateTime dateSubmitted;
    private Enums.Status applicationStatus;
    private Enums.State applicationState;

}