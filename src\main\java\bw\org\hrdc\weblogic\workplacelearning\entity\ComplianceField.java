package bw.org.hrdc.weblogic.workplacelearning.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.UUID;

@Entity
@Table(name = "compliance_field")
@Getter
@Setter
@ToString
public class ComplianceField {

    @Id
    @GeneratedValue
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "criteria_id", nullable = false)
    private ComplianceCriteria criteria;

    @Column(name = "label", nullable = false)
    private String label;

    @Column(name = "comments")
    private String comments;

    @Column(name = "observations")
    private String observations;

    @Column(name = "compliant")
    private Boolean compliant;
}

