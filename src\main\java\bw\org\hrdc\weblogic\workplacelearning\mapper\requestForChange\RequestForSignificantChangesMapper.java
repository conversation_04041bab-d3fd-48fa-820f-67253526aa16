package bw.org.hrdc.weblogic.workplacelearning.mapper.requestForChange;

import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.RequestForSignificantChangesDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.RequestForSignificantChangesListDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RFCShortCourseInformation;
import bw.org.hrdc.weblogic.workplacelearning.entity.requestForChange.RequestForSignificantChanges;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Mapper for converting between RequestForSignificantChanges
 * entity and RequestForSignificantChangesDto.
 */
@Component
public class RequestForSignificantChangesMapper {

    public static RequestForSignificantChangesDto toDto(RequestForSignificantChanges entity) {
        if (entity == null) {
            return null;
        }

        RequestForSignificantChangesDto model = new RequestForSignificantChangesDto();
//        model.setUuid(UUID.fromString(entity.getUuid()));
        model.setOrganisationId(entity.getOrganisationId());
        model.setNcbscApplicationId(entity.getNcbscApplicationId());
        if(entity.getAssignedTo() != null) model.setAssignedTo(UUID.fromString(entity.getAssignedTo().toString()));
        model.setRequestedBy(entity.getRequestedBy());
        model.setJustification(entity.getJustification());
        model.setAssessmentPurpose(entity.getAssessmentPurpose());
        model.setSkillsNeedsAnalysis(entity.getSkillsNeedsAnalysis());
        model.setShortCourseDeliveryMode(entity.getShortCourseDeliveryMode());
        model.setKeyFacilitation(entity.getKeyFacilitation());
        model.setSkillsAssessment(entity.getSkillsAssessment());
        model.setAssessmentType(entity.getAssessmentType());
        model.setCertification(entity.getCertification());
        model.setResources(entity.getResources());
        model.setThirdPartyArrangements(entity.getThirdPartyArrangements());
        model.setShortCourseEndorsement(entity.getShortCourseEndorsement());
        model.setApplicationStatus(entity.getApplicationStatus().toString());
        model.setApplicationState(entity.getApplicationState().toString());
        model.setCreatedAt(entity.getCreatedAt());
        model.setUpdatedAt(entity.getUpdatedAt());
        model.setDateSubmitted(entity.getDateSubmitted());

        model.setAssignedAgent(entity.getAssignedAgent());
        model.setAssignedAgentLead(entity.getAssignedAgentLead());
        model.setAssignedOfficerLead(entity.getAssignedOfficerLead());
        model.setAssignedOfficer(entity.getAssignedOfficer());
        model.setAssignedManager(entity.getAssignedManager());
        model.setScopeOfAccreditation(convertStringToList(entity.getScopeOfAccreditation()));

        RFCShortCourseInformation shortCourseInformationDto = entity.getShortCourseInformation();
        // Preventing potential null lists from throwing .stream() errors
        if (shortCourseInformationDto != null) {
            model.setShortCourseInformation(RFCShortCourseInformationMapper.toDto(shortCourseInformationDto));
        }

        if (entity.getCourseContentAndDelivery() != null) {
            model.setCourseContentAndDelivery(RFCCourseContentAndDeliveryMapper.toDto(entity.getCourseContentAndDelivery()));
        }
        model.setCourseDeliverySchedule(entity.getCourseDeliverySchedule() != null
                ? RFCCourseDeliveryScheduleMapper.toDtoList(entity.getCourseDeliverySchedule())
                : Collections.emptyList());

        model.setLearningOutcomes(entity.getLearningOutcomes() != null
                ? RFCLearningOutcomeMapper.toDtoList(entity.getLearningOutcomes())
                : Collections.emptyList());

        model.setDetailsOfSignificantChanges(entity.getDetailsOfSignificantChanges() != null
                ? RFCDetailsOfSignificantChangeMapper.toDtoList(entity.getDetailsOfSignificantChanges())
                : Collections.emptyList());

        return model;
    }

    public static List<String> convertStringToList(String arrayAsString) {
        if (arrayAsString == null || arrayAsString.isEmpty()) {
            return List.of(); // Return an empty list if input is null or empty
        }

        return Arrays.stream(arrayAsString.replaceAll("[\\[\\]]", "").split(",\\s*"))
                .collect(Collectors.toList());
    }

    public static RequestForSignificantChanges toEntity(RequestForSignificantChangesDto dto) {
        if (dto == null) {
            return null;
        }

        RequestForSignificantChanges entity = new RequestForSignificantChanges();

        // Set basic fields
        entity.setOrganisationId(dto.getOrganisationId());
        entity.setNcbscApplicationId(dto.getNcbscApplicationId());
        entity.setJustification(dto.getJustification());
        entity.setAssessmentPurpose(dto.getAssessmentPurpose());
        entity.setSkillsNeedsAnalysis(dto.getSkillsNeedsAnalysis());
        entity.setShortCourseDeliveryMode(dto.getShortCourseDeliveryMode());
        entity.setKeyFacilitation(dto.getKeyFacilitation());
        entity.setSkillsAssessment(dto.getSkillsAssessment());
        entity.setAssessmentType(dto.getAssessmentType());
        entity.setCertification(dto.getCertification());
        entity.setResources(dto.getResources());
        entity.setThirdPartyArrangements(dto.getThirdPartyArrangements());
        entity.setShortCourseEndorsement(dto.getShortCourseEndorsement());
        entity.setScopeOfAccreditation(String.join(",", dto.getScopeOfAccreditation())); // Store as a single string

        entity.setAssignedAgent(dto.getAssignedAgent());
        entity.setAssignedAgentLead(dto.getAssignedAgentLead());
        entity.setAssignedOfficerLead(dto.getAssignedOfficerLead());
        entity.setAssignedOfficer(dto.getAssignedOfficer());
        entity.setAssignedManager(dto.getAssignedManager());

        return entity;
    }

    public static RequestForSignificantChangesListDto toDtoList(RequestForSignificantChanges entity){
        if(entity == null){
            return null;
        }

        RequestForSignificantChangesListDto model = new RequestForSignificantChangesListDto();
        model.setReferenceNumber(entity.getReferenceNumber());
        model.setOrganisationId(entity.getOrganisationId());
        if(entity.getAssignedTo() != null) model.setAssignedTo(entity.getAssignedTo().toString());
        model.setDateSubmitted(LocalDateTime.parse(entity.getCreatedAt().toString()));
        model.setApplicationStatus(entity.getApplicationStatus().toString());
        model.setApplicationState(entity.getApplicationState().toString());
        return model;
    }
}
