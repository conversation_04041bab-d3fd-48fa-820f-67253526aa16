package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * DTO for managing Training Details.
 */
@Data
@Schema(
        name = "TrainingDetail",
        description = "Schema to hold details of individual training programmes"
)
public class TrainingDetailDto {

    @Schema(description = "Unique identifier for the training detail", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @NotNull(message = "Workplace Training Plan ID cannot be null")
    @Schema(description = "Unique identifier for the associated training plan", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID workplaceTrainingPlanId;

    @NotNull(message = "Learning programme cannot be null")
    @Schema(description = "Name of the learning programme", example = "Advanced Project Management")
    private String learningProgramme;

    @NotNull(message = "Skills to be acquired cannot be null")
    @Schema(description = "Skills to be acquired through the training", example = "Leadership and Communication")
    private String skillsToBeAcquired;

    @NotNull(message = "Training dates cannot be null")
    @Schema(description = "Scheduled dates for the training", example = "2024-01-15")
    private Date trainingDates;

    @NotNull(message = "Institution name cannot be null")
    @Schema(description = "Name of the training institution", example = "Botswana Training Institute")
    private String institutionName;

    @NotNull(message = "Location cannot be null")
    @Schema(description = "Location of the training", example = "Gaborone")
    private String location;

    @NotNull(message = "Accrediting body cannot be null")
    @Schema(description = "Accrediting body for the training", example = "HRDC")
    private String accreditingBody;

    @NotNull(message = "Level of training cannot be null")
    @Schema(description = "Level of the training", example = "Intermediate")
    private String levelOfTraining;

    @NotNull(message = "Cost of training cannot be null")
    @Schema(description = "Cost of the training", example = "1200.00")
    private Double costOfTraining;

    @NotNull(message = "Number of participants cannot be null")
    @Schema(description = "Number of participants attending the training", example = "25")
    private Integer numberOfParticipants;

    @NotNull(message = "Citizen status cannot be null")
    @Schema(description = "Citizen status of the participants", example = "Citizen")
    private String citizenStatus;

    @NotNull(message = "Total number of people to be trained cannot be null")
    @Schema(description = "Total number of people to be trained", example = "30")
    private Integer totalPeopleToBeTrained;
}
