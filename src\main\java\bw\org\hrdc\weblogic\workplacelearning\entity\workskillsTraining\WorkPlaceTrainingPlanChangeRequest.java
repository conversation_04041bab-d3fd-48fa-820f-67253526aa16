package bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining;

import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.CourseDetailsDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.base.Assignment;
import bw.org.hrdc.weblogic.workplacelearning.util.converters.CourseDetailsListConverter;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.ColumnTransformer;

import java.util.ArrayList;
import java.util.List;
import java.time.LocalDate;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "training_plan_change_request")
@Data
@ToString
public class WorkPlaceTrainingPlanChangeRequest extends Assignment {

    @Column(name = "organisation_id", nullable = false)
    private String organisationId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "training_plan_id", nullable = false)
    private WorkPlaceTrainingPlan trainingPlan;

    @Column(name = "application_status")
    private String applicationStatus;

    @Column(name = "application_state")
    private String applicationState;

    @Column(name = "application_number")
    private String applicationNumber;

    @Column(name = "reference_number")
    private String referenceNumber;

    @Column(name = "financial_year")
    private String financialYear;

    @Column(name = "location")
    private String location;

    @Column(name = "contact_date")
    private LocalDate contactDate;

    @Column(name = "submission_date")
    private LocalDate submissionDate;

    @Column(name = "course_details", columnDefinition = "jsonb")
    @Convert(converter = CourseDetailsListConverter.class)
    @ColumnTransformer(write = "?::jsonb")
    private List<CourseDetailsDto> courseDetails = new ArrayList<>();

    @Column(name = "process_instance_id")
    private String processInstanceId;

    @PrePersist
    protected void onCreate() {
        setCreatedAt(LocalDateTime.now());
        setUpdatedAt(LocalDateTime.now());
        setDeleted(false);

        // Set contact date to current date if not already set
        if (contactDate == null) {
            contactDate = LocalDate.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        setUpdatedAt(LocalDateTime.now());
    }
}
