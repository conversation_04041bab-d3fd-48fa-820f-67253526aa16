package bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "assessment_criterion")
@Data
public class AssessmentCriteria extends Base implements Serializable {

    @Column(name = "topic")
    private String topic;

    @Column(name = "objective")
    private String objective;

    @Column(name = "delivery_strategy")
    private String deliveryStrategy;

    @Column(name = "assessment_strategy")
    private String assessmentStrategy;

    @Column(name = "date_from")
    private Date dateFrom;

    @Column(name = "date_to")
    private Date dateTo;

    @Column(name = "hours", nullable = true)
    private Integer hours;

    @ManyToOne
    @JoinColumn(name = "learning_outcome", nullable = false)
    @JsonIgnore
    @ToString.Exclude
    private LearningOutcome learningOutcomes;
}
