package bw.org.hrdc.weblogic.workplacelearning.entity;

import bw.org.hrdc.weblogic.workplacelearning.constants.DocumentStatus;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Created by <PERSON>of<PERSON> on 26 November 2024
 */

@Entity
@Table(name = "document")
@EntityListeners(AuditingEntityListener.class)
@Getter
@Setter
@ToString
public class Document {

    @Id
    @GeneratedValue
    private UUID id;

    @Column(nullable = false)
    private String name;

    @Enumerated(EnumType.STRING)
    private Enums.DocumentType documentType;

    @Column(name = "file_path", nullable = false)
    private String filePath;

    @Enumerated(EnumType.STRING)
    private DocumentStatus status;

    @Column(name = "organisation_id", nullable = false)
    private UUID organisationId;

    @ManyToOne
    @JoinColumn(name = "non_credit_bearing_course_id", nullable = false)
    private NonCreditBearingCourse nonCreditBearingCourse;

    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createdAt;

    @CreatedBy
    @Column(updatable = false)
    private String createdBy;

    @LastModifiedDate
    @Column(insertable = false)
    private LocalDateTime updatedAt;

    @LastModifiedBy
    @Column(insertable = false)
    private String updatedBy;
}
