package bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.CourseDeliveryScheduleDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.CourseDeliverySchedule;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * Mapper for converting between CourseDeliverySchedule entity and CourseDeliveryScheduleDto.
 */
@Component
public class CourseDeliveryScheduleMapper {

    public static CourseDeliverySchedule toEntity(CourseDeliveryScheduleDto model, NCBSCApplication application) {
        if (model == null) {
            return null;
        }
        CourseDeliverySchedule schedule =  new CourseDeliverySchedule();
        schedule.setDate(model.getDate());
        schedule.setTopic(model.getTopic());
        schedule.setHours(model.getHours());
        schedule.setApplication(application);
        return schedule;
    }

    public static CourseDeliveryScheduleDto toDto(CourseDeliverySchedule entity) {
        if (entity == null) {
            return null;
        }
        CourseDeliveryScheduleDto model = new CourseDeliveryScheduleDto();
        model.setDate(entity.getDate());
        model.setTopic(entity.getTopic());
        model.setHours(entity.getHours());

        return model;
    }

    public static Set<CourseDeliverySchedule> toEntityList(Set<CourseDeliveryScheduleDto> dtos, NCBSCApplication application) {

        if (dtos == null) {
            return null;
        }
        return dtos.stream()
                .map(schedule ->CourseDeliveryScheduleMapper.toEntity(schedule, application))
                .collect(Collectors.toSet());
    }

    public static Set<CourseDeliveryScheduleDto> toDtoList(Set<CourseDeliverySchedule> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(CourseDeliveryScheduleMapper::toDto)
                .collect(Collectors.toSet());
    }
}
