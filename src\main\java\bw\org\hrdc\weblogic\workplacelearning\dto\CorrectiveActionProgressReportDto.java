package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * DTO for corrective action progress report in ETP evaluation.
 */
@Data
@Schema(
        name = "CorrectiveActionProgressReport",
        description = "Schema to hold details of a corrective action progress report in ETP evaluation"
)
public class CorrectiveActionProgressReportDto {

    @Schema(description = "Unique identifier for the progress report", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @Schema(description = "Weight of the non-conformance", example = "Medium")
    private String weight;

    @Schema(description = "Description of the progress made", example = "50% of the updates have been completed.")
    private String description;

    @Schema(description = "Person or team responsible for the progress", example = "<PERSON>")
    private String actionAgent;

    @Schema(description = "Expected completion date for the corrective action", example = "2024-12-15")
    private Date completionDate;
}
