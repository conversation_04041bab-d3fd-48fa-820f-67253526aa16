package bw.org.hrdc.weblogic.workplacelearning.mapper;

import bw.org.hrdc.weblogic.workplacelearning.dto.ComplaintDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.Complaint;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between Complaint entity and ComplaintDto.
 */
@Component
public class ComplaintMapper {

    private final ModelMapper modelMapper;

    @Autowired
    public ComplaintMapper(ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
    }

    // Convert Entity to DTO
    public ComplaintDto toDto(Complaint entity) {
        return modelMapper.map(entity, ComplaintDto.class);
    }

    // Convert DTO to Entity
    public Complaint toEntity(ComplaintDto dto) {
        return modelMapper.map(dto, Complaint.class);
    }

    // Convert Page<Complaint> to Page<ComplaintDto>
    public Page<ComplaintDto> toDtoPage(Page<Complaint> entityPage) {
        return entityPage.map(this::toDto);
    }

    // Convert List<Complaint> to List<ComplaintDto>
    public List<ComplaintDto> toDtoList(List<Complaint> entityList) {
        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}