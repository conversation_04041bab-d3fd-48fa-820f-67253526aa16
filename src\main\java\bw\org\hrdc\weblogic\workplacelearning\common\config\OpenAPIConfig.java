package bw.org.hrdc.weblogic.workplacelearning.common.config;

import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;

@Configuration
public class OpenAPIConfig {

    /**
     * Create a custom OpenAPI bean with the API title and description.
     *
     * @return the custom OpenAPI bean
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Workplace Learning API")
                        .version("1.0")
                        .description("API for HRDC workplace learning Application"));
    }

    /**
     * Create a custom OpenAPI bean with the API title and description.
     *
     * @return the custom OpenAPI bean
     */
    @Bean
    public GroupedOpenApi publicApi() {
        return GroupedOpenApi.builder()
                .group("public-apis")
                .pathsToMatch("/api/**")
                .build();
    }
}
