package bw.org.hrdc.weblogic.workplacelearning.entity.complaint;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Auditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 27/03/25 20:50
 * @UpdatedBy martinspectre
 * @UpdatedOn 27/03/25 20:50
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "comments")
@Getter
@Setter
public class Comment extends Auditable implements Serializable {
    @ManyToOne
    @JoinColumn(name = "complaint_id", nullable = false)
    @JsonIgnore
    private ComplaintEntity complaint;

    @ManyToOne
    @JoinColumn(name = "parent_comment_id")
    @JsonIgnore
    private Comment parentComment;

    private String message;

    private String outcome = "Pending";

    @OneToMany(mappedBy = "parentComment", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnore
    private List<Comment> replies = new ArrayList<>();

    @OneToMany(mappedBy = "comment", cascade = CascadeType.ALL, orphanRemoval = true)
//    @JsonIgnore
    private List<CommentDocument> documents = new ArrayList<>();
}
