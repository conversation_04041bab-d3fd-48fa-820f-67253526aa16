package bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.ShortCourseInformationListDto;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @CreatedOn 26/03/25 16:47
 * @UpdatedBy martinspectre
 * @UpdatedOn 26/03/25 16:47
 */
public class ShortCourseRowMapper implements RowMapper<ShortCourseInformationListDto> {
    @Override
    public ShortCourseInformationListDto mapRow(ResultSet rs, int rowNum) throws SQLException {
        return new ShortCourseInformationListDto(
                rs.getString("uuid"),
                rs.getString("title"),
                rs.getString("type"),
                rs.getString("start_date"),
                rs.getString("end_date"),
                rs.getString("level"),
                rs.getString("accrediting_body"),
                rs.getString("exit_level_outcomes")
        );
    }
}
