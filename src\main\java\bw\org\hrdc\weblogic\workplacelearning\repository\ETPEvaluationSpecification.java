package bw.org.hrdc.weblogic.workplacelearning.repository;

import bw.org.hrdc.weblogic.workplacelearning.entity.ETPEvaluation;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;
import java.util.UUID;

/**
 * Specification for ETPEvaluation entity.
 */
public class ETPEvaluationSpecification {

    /**
     * Creates a specification for filtering ETPEvaluations by various criteria.
     *
     * @param meetingDate   the date of the meeting.
     * @param userId        the ID of the user who created the evaluation.
     * @param organisationId the ID of the organisation being evaluated.
     * @return a specification for filtering ETPEvaluations.
     */
    public static Specification<ETPEvaluation> searchByCriteria(
            Date meetingDate, UUID userId, UUID organisationId) {
        return (root, query, cb) -> {
            Predicate p = cb.conjunction();

            if (meetingDate != null) {
                p = cb.and(p, cb.equal(root.get("meetingDate"), meetingDate));
            }
            if (userId != null) {
                p = cb.and(p, cb.equal(root.get("userId"), userId));
            }
            if (organisationId != null) {
                p = cb.and(p, cb.equal(root.get("organisationId"), organisationId));
            }

            return p;
        };
    }
}
