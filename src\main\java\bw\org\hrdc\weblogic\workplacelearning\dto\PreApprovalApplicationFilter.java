package bw.org.hrdc.weblogic.workplacelearning.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Filter criteria for Pre-Approval Applications.
 * Used to filter applications based on various criteria.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreApprovalApplicationFilter {
    private UUID organisationId;
    private UUID userId;
    private String applicationStatus;
    private String state;
    private String trainingProvider;
    private String courseTitle;
    private LocalDateTime startDateTime;
    private LocalDateTime endDateTime;
    private String assignedTo;
    private String role;
    private boolean includeDeleted;
} 