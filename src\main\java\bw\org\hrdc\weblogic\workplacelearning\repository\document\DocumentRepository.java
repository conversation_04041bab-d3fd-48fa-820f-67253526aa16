package bw.org.hrdc.weblogic.workplacelearning.repository.document;

import bw.org.hrdc.weblogic.workplacelearning.entity.document.DocumentCommon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @CreatedOn 23/06/25 12:10
 * @UpdatedBy martinspectre
 * @UpdatedOn 23/06/25 12:10
 */
@Repository
public interface DocumentRepository extends JpaRepository<DocumentCommon, UUID>, JpaSpecificationExecutor<DocumentCommon> {
    @Query("SELECT o FROM DocumentCommon o WHERE o.identifier = :identifier")
    List<DocumentCommon> findByIdentifier(String identifier);
}
