package bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.AssessmentCriteria;
import jakarta.transaction.Transactional;
import lombok.NonNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 22/02/25 11:08
 * @UpdatedBy martinspectre
 * @UpdatedOn 22/02/25 11:08
 */
@Repository
public interface AssessmentCriteriaRepo extends JpaRepository<AssessmentCriteria, Long>, JpaSpecificationExecutor<AssessmentCriteria> {

    @Query(value = "SELECT * FROM assessment_criterion a WHERE a.learning_outcome = :learningOutcome", nativeQuery = true)
    @NonNull
    List<AssessmentCriteria> findByLearningOutcomeId(@NonNull String learningOutcome);

    @Query("SELECT ac FROM AssessmentCriteria ac " +
            "JOIN ac.learningOutcomes lo " +
            "JOIN lo.courseContentDelivery ccd " +
            "JOIN ccd.application a " +
            "WHERE a.shortCourseInformation.uuid = :courseId")
    List<AssessmentCriteria> findByCourseId(@Param("courseId") String courseId);

    @Modifying
    @Transactional
    @Query(
            value = "INSERT INTO assessment_criterion (id, uuid, topic, objective, delivery_strategy, assessment_strategy, learning_outcome) " +
                    "VALUES (:id, :uuid, :topic, :objective, :deliveryStrategy, :assessmentStrategy, :learningOutcomeId) " +
                    "ON CONFLICT (uuid) DO UPDATE SET " +
                    "topic = EXCLUDED.topic, " +
                    "objective = EXCLUDED.objective, " +
                    "delivery_strategy = EXCLUDED.delivery_strategy, " +
                    "assessment_strategy = EXCLUDED.assessment_strategy, " +
                    "learning_outcome = EXCLUDED.learning_outcome",
            nativeQuery = true
    )
    void upsertCriteria(
            @Param("id") Long id,
            @Param("uuid") String uuid,
            @Param("topic") String topic,
            @Param("objective") String objective,
            @Param("deliveryStrategy") String deliveryStrategy,
            @Param("assessmentStrategy") String assessmentStrategy,
            @Param("learningOutcomeId") String learningOutcomeId
    );
}
