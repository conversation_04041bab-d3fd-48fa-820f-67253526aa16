package bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc;

import lombok.AllArgsConstructor;
import lombok.Data;
import java.util.List;

@Data
public class BatchStatusUpdateResult {
    private int totalProcessed;
    private int successCount;
    private int failureCount;
    private List<ApplicationUpdateResult> results;
    
    @Data
    @AllArgsConstructor
    public static class ApplicationUpdateResult {
        private String referenceNumber;
        private boolean success;
        private String message;
    }
}