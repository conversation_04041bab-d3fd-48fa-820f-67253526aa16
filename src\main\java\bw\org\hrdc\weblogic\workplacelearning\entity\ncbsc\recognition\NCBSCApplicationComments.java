package bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "ncbsc_application_comments")
@Getter @Setter
public class NCBSCApplicationComments extends Base implements Serializable {

    @Column(nullable = false)
    private String action;  // e.g., "APPROVED", "REJECTED", "ASSIGNED"

    @Column(length = 500)
    private String comments;

    @Column(nullable = false)
    private String updatedBy;

    @Column(nullable = false)
    private LocalDateTime timestamp = LocalDateTime.now();

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ncbsc_application_id", nullable = true)
    @JsonIgnore
    private NCBSCApplication ncbscApplication;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "noc_application_id", nullable = true)
    @JsonIgnore
    private NOCApplication nocApplication;
    
    @Column(name = "batch_id", columnDefinition = "uuid")
    private UUID batchId;
}
