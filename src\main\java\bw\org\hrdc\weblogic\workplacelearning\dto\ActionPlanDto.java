package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * DTO for action plan in ETP evaluation.
 */
@Data
@Schema(
        name = "ActionPlan",
        description = "Schema to hold details of an action plan in ETP evaluation"
)
public class ActionPlanDto {

    @Schema(description = "Weight of the non-conformance", example = "High")
    private String weight;

    @Schema(description = "Description of the issue or recommendation", example = "Update training materials.")
    private String description;

    @Schema(description = "Person or team responsible for the action", example = "<PERSON>")
    private String actionAgent;

    @Schema(description = "Date by which the action should be completed", example = "2024-12-31")
    private Date completionDate;
}
