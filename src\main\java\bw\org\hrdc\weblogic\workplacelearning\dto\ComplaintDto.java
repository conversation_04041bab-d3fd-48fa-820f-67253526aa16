package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.UUID;

/**
 * DTO for managing Complaint details.
 */
@Data
@Schema(
        name = "Complaint",
        description = "Schema to hold complaint details"
)
public class ComplaintDto {

    @Schema(description = "Unique identifier for the complaint", example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @NotNull(message = "Appellant name cannot be null")
    @Schema(description = "Name of the person filing the complaint", example = "John Doe")
    private String appellantName;

    @NotNull(message = "Appeal date cannot be null")
    @Schema(description = "Date when the appeal was made", example = "2023-10-01")
    private LocalDate appealDate;

    @NotNull(message = "Reason for the complaint cannot be null")
    @Schema(description = "Reason for the complaint", example = "Delayed response to application")
    private String reason;

    @NotNull(message = "Status of the complaint cannot be null")
    @Schema(description = "Current status of the complaint", example = "Open")
    private String status;

    @NotNull(message = "Date submitted cannot be null")
    @Schema(description = "Date when the complaint was submitted", example = "2023-10-02")
    private LocalDate dateSubmitted;

    @Schema(description = "ID of the application related to the complaint", example = "123e4567-e89b-12d3-a456-************")
    private UUID applicationId;

    @Schema(description = "Type of application", example = "Non-Credit")
    private String applicationType;

    @Schema(description = "Date when the complaint was reviewed", example = "2023-10-05")
    private LocalDate dateReviewed;

    @Schema(description = "Date when a response was provided", example = "2023-10-07")
    private LocalDate dateResponded;

    @Schema(description = "Date when the complaint was closed", example = "2023-10-10")
    private LocalDate dateClosed;

    @NotNull(message = "User ID cannot be null")
    @Schema(description = "ID of the user who filed the complaint", example = "123e4567-e89b-12d3-a456-************")
    private UUID userId;

    @NotNull(message = "Organisation ID cannot be null")
    @Schema(description = "ID of the organisation associated with the complaint", example = "123e4567-e89b-12d3-a456-************")
    private UUID organisationId;
}