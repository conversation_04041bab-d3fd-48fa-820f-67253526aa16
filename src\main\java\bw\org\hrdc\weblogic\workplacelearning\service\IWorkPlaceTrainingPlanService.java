package bw.org.hrdc.weblogic.workplacelearning.service;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import bw.org.hrdc.weblogic.workplacelearning.dto.*;
import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.CourseDetailsDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.WorkPlaceTrainingPlanDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.WorkPlaceTrainingPlanResponseDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.CourseDetails;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Service interface for WorkPlaceTrainingPlan.
 */
public interface IWorkPlaceTrainingPlanService {


    /**
     * Creates or updates a Work Place Training Plan based on whether it's a draft.
     *
     * @param trainingPlanDto the DTO containing training plan details.
     * @param isDraft whether the training plan is a draft.
     * @return the response DTO with status and IDs
     */
    WorkPlaceTrainingPlanResponseDto createOrUpdateTrainingPlan(WorkPlaceTrainingPlanDto trainingPlanDto, boolean isDraft);

    /**
     * Updates an existing Work Place Training Plan.
     *
     * @param trainingPlanDto the DTO containing updated training plan details.
     */
    void updateTrainingPlan(WorkPlaceTrainingPlanDto trainingPlanDto);

    /**
     * Retrieves a Work Place Training Plan by its ID.
     *
     * @param id the unique identifier of the training plan.
     * @return the DTO containing training plan details.
     */
    WorkPlaceTrainingPlanDto getTrainingPlan(UUID id);


    /**
     * Retrieves a Pre-Approval Application by its reference number.
     *
     * @param referenceNumber the reference number
     * @return Optional containing the application if found
     */
    WorkPlaceTrainingPlanDto getApplicationByReferenceNumber(String referenceNumber);

    /**
     * Retrieves a Pre-Approval Application by its reference number.
     *
     * @param referenceNumber the reference number
     * @return Optional containing the application if found
     */
    WorkPlaceTrainingPlan fetchByReferenceNumber(String referenceNumber);


    /**
     * Soft deletes a Work Place Training Plan by its ID.
     *
     * @param id the unique identifier of the training plan to soft delete.
     */
    @Transactional
    void softDeleteTrainingPlan(UUID id);

    /**
     * Fetches training plans based on role and various filter criteria
     * 
     * @param role the user role
     * @param userId the user ID
     * @param organisationId the organisation ID
     * @param applicationStatus the application status
     * @param applicationState the application state
     * @param submissionDate the submission date for filtering
     * @param programme the programme name for filtering (exact match)
     * @param startDate the start date for filtering
     * @param endDate the end date for filtering
     * @param search the unified search term for filtering across multiple fields
     * @param referenceNumber the reference number for filtering
     * @param companyName the company name for filtering
     * @param pageNumber the page number
     * @param size the page size
     * @return Map containing metadata and content
     */
    Map<String, Object> getTrainingPlansByRole(
            String role, UUID userId, UUID organisationId, String applicationStatus, String applicationState,
            Date submissionDate, String programme, Date startDate, Date endDate,
            String search, String referenceNumber, String companyName, int pageNumber, int size);

    /**
     * Creates an empty response with default metadata
     * 
     * @param pageNumber the page number
     * @param size the page size
     * @return Map containing empty metadata and content
     */
    Map<String, Object> createEmptyResponse(int pageNumber, int size);

    /**
     * Get WorkPlace Training Plan entity by ID
     * @param id The training plan ID
     * @return Optional containing the training plan entity if found
     */
    Optional<WorkPlaceTrainingPlan> getTrainingPlanEntity(UUID id);

    /**
     * Update the assigned user for a WorkPlace Training Plan
     * @param id The training plan ID
     * @param role The role of the user being assigned
     * @param userId The ID of the user being assigned
     * @return Number of records updated
     */
    int updateTrainingPlanAssignedUser(UUID id, Enums.UserRoles role, String userId);

    /**
     * Updates the status of a workplace training plan
     * @param trainingPlanId The ID of the training plan to update
     * @param role The role of the user making the update
     * @param action The action being performed
     * @param newAssignee The new assignee (if any)
     * @return The number of records updated (1 for success, 0 for failure)
     */
    int updateTrainingPlanStatus(UUID trainingPlanId, String role, String action, String newAssignee);

    /**
     * Sanitizes HTML content to prevent XSS attacks
     * @param content The content to sanitize
     * @return The sanitized content
     */
    String sanitizeHtml(String content);

    /**
     * Updates the status of multiple training plans in a batch
     * @param applicationIds List of training plan IDs to update
     * @param role Role of the user performing the update
     * @param action Action to perform
     * @param userId ID of the user performing the update
     * @param comments Comments for the status update
     * @param newAssignee New assignee for the training plans
     * @return BatchStatusUpdateResult containing the results of the batch update
     */
    BatchStatusUpdateResult batchUpdateTrainingPlanStatus(
            List<UUID> applicationIds,
            String role,
            String action,
            String userId,
            String comments,
            String newAssignee);

    /**
     * Get Course Details by ID
     * @param id The Course Details ID
     * @return Optional containing the Course Details entity if found
     */
    Optional<CourseDetails> getCourseDetailsEntity(UUID id);

    void updateProcessInstanceIdToApplication(String preApprovalId, String processInstanceId);
}
