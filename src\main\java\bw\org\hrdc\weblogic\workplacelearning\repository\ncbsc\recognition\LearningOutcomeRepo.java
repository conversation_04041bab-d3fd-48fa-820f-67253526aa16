package bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.LearningOutcome;
import jakarta.transaction.Transactional;
import lombok.NonNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @CreatedOn 22/02/25 11:08
 * @UpdatedBy martinspectre
 * @UpdatedOn 22/02/25 11:08
 */
@Repository
public interface LearningOutcomeRepo extends JpaRepository<LearningOutcome, Long>, JpaSpecificationExecutor<LearningOutcome> {
    @Query(value = "SELECT * FROM learning_outcomes a WHERE a.course_content_id = :courseContentId", nativeQuery = true)
    @NonNull
    List<LearningOutcome> findByCourseContentId(@NonNull String courseContentId);

    @Query("SELECT lo FROM LearningOutcome lo " +
            "WHERE lo.uuid = :uuid")
    Optional<LearningOutcome> findByUuid(@Param("uuid") String uuid);

    @Modifying
    @Transactional
    @Query(value = """
    INSERT INTO learning_outcomes (id, uuid, outcome, course_content_id)
    VALUES (:id, :uuid, :outcome, :courseContentId)
    ON CONFLICT (uuid) DO UPDATE SET
        outcome = EXCLUDED.outcome,
        course_content_id = EXCLUDED.course_content_id
    """, nativeQuery = true)
    void upsertLearningOutcome(
            @Param("id") Long id,
            @Param("uuid") String uuid,
            @Param("outcome") String outcome,
            @Param("courseContentId") String courseContentId
    );
}
