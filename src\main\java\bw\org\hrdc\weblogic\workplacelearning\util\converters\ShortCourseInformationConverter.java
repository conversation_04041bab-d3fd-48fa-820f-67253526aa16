package bw.org.hrdc.weblogic.workplacelearning.util.converters;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ShortCourseInformation;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * <AUTHOR>
 * @CreatedOn 14/04/25 10:09
 * @UpdatedBy martinspectre
 * @UpdatedOn 14/04/25 10:09
 */
@Converter
public class ShortCourseInformationConverter implements AttributeConverter<ShortCourseInformation, String> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String convertToDatabaseColumn(ShortCourseInformation attribute) {
        try {
            return objectMapper.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize ShortCourseInformation", e);
        }
    }

    @Override
    public ShortCourseInformation convertToEntityAttribute(String dbData) {
        try {
            return objectMapper.readValue(dbData, ShortCourseInformation.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to deserialize ShortCourseInformation", e);
        }
    }
}
