package bw.org.hrdc.weblogic.workplacelearning.rfai.service.impl;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import bw.org.hrdc.weblogic.workplacelearning.exception.ResourceNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.rfai.dto.*;
import bw.org.hrdc.weblogic.workplacelearning.rfai.entity.AdditionalInformation;
import bw.org.hrdc.weblogic.workplacelearning.rfai.entity.EtpDetails;
import bw.org.hrdc.weblogic.workplacelearning.rfai.entity.RequestForAdditionalInformation;
import bw.org.hrdc.weblogic.workplacelearning.rfai.repository.RequestForAdditionalInformationRepository;
import bw.org.hrdc.weblogic.workplacelearning.rfai.service.RequestForAdditionalInformationService;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class RequestForAdditionalInformationServiceImpl implements RequestForAdditionalInformationService {
    private final RequestForAdditionalInformationRepository requestForAdditionalInformationRepository;

    public RequestForAdditionalInformationServiceImpl(RequestForAdditionalInformationRepository requestForAdditionalInformationRepository) {
        this.requestForAdditionalInformationRepository = requestForAdditionalInformationRepository;
    }

    public RequestForAdditionalInformationResponse createAdditionalInformation(RequestForAdditionalInformationRequest request) {
        RequestForAdditionalInformation application = mapToEntity(request);
        application.setDateSubmitted(LocalDateTime.now());
        RequestForAdditionalInformation savedApplication = requestForAdditionalInformationRepository.save(application);
        return mapToResponse(savedApplication);
    }

    public List<RequestForAdditionalInformationResponse> getAllAdditionalInformation() {
        return requestForAdditionalInformationRepository.findAll().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public RequestForAdditionalInformationResponse getAdditionalInformationById(Long id) {
        RequestForAdditionalInformation application = requestForAdditionalInformationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Application not found"));
        return mapToResponse(application);
    }

    public RequestForAdditionalInformationResponse updateAdditionalInformation(Long id, RequestForAdditionalInformationRequest request) {
        RequestForAdditionalInformation existingApplication = requestForAdditionalInformationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Application not found"));
        updateEntity(existingApplication, request);
        RequestForAdditionalInformation updatedApplication = requestForAdditionalInformationRepository.save(existingApplication);
        return mapToResponse(updatedApplication);
    }

    public void deleteApplication(Long id) {
        requestForAdditionalInformationRepository.deleteById(id);
    }

    private RequestForAdditionalInformation mapToEntity(RequestForAdditionalInformationRequest request) {
        RequestForAdditionalInformation application = new RequestForAdditionalInformation();
        application.setOrganisationId(request.getOrganisationId());
        application.setSubmittedByUserId(request.getSubmittedByUserId());
        application.setStatus(request.getStatus());
        application.setEtpDetails(mapToEtpDetails(request.getEtpDetails()));
        application.getAdditionalInformationRequired().clear();
        for (AdditionalInformationRequest air : request.getAdditionalInformationRequired()) {
            AdditionalInformation ai = new AdditionalInformation();
            ai.setCriteria(air.getCriteria());
            ai.setDetailsOfAdditionalInformationRequired(air.getDetailsOfAdditionalInformationRequired());
            ai.setProvided(air.isProvided());
            ai.setRequestForAdditionalInformation(application);
            application.getAdditionalInformationRequired().add(ai);
        }
        return application;
    }

    private EtpDetails mapToEtpDetails(EtpDetailsDTO dto) {
        EtpDetails details = new EtpDetails();
        details.setNameOfEducationProvider(dto.getNameOfEducationProvider());
        details.setRegistrationAccreditationNumber(dto.getRegistrationAccreditationNumber());
        details.setCourseTitle(dto.getCourseTitle());
        details.setFieldNumber(dto.getFieldNumber());
        details.setCategoryOfETP(dto.getCategoryOfETP());
        return details;
    }

    private RequestForAdditionalInformationResponse mapToResponse(RequestForAdditionalInformation application) {
        RequestForAdditionalInformationResponse response = new RequestForAdditionalInformationResponse();
        response.setId(application.getId());
        response.setOrganisationId(application.getOrganisationId());
        response.setSubmittedByUserId(application.getSubmittedByUserId());
        response.setDateSubmitted(application.getDateSubmitted());
        response.setStatus(application.getStatus());
        response.setEtpDetails(mapToEtpDetailsDTO(application.getEtpDetails()));
        response.setAdditionalInformationRequired(
                application.getAdditionalInformationRequired().stream()
                        .map(this::mapToAdditionalInfoResponse)
                        .collect(Collectors.toList())
        );
        return response;
    }

    private EtpDetailsDTO mapToEtpDetailsDTO(EtpDetails details) {
        EtpDetailsDTO dto = new EtpDetailsDTO();
        dto.setNameOfEducationProvider(details.getNameOfEducationProvider());
        dto.setRegistrationAccreditationNumber(details.getRegistrationAccreditationNumber());
        dto.setCourseTitle(details.getCourseTitle());
        dto.setFieldNumber(details.getFieldNumber());
        dto.setCategoryOfETP(details.getCategoryOfETP());
        return dto;
    }

    private AdditionalInformationResponse mapToAdditionalInfoResponse(AdditionalInformation ai) {
        AdditionalInformationResponse response = new AdditionalInformationResponse();
        response.setId(ai.getId());
        response.setCriteria(ai.getCriteria());
        response.setDetailsOfAdditionalInformationRequired(ai.getDetailsOfAdditionalInformationRequired());
        response.setProvided(ai.isProvided());
        return response;
    }

    private void updateEntity(RequestForAdditionalInformation existing, RequestForAdditionalInformationRequest request) {
        existing.setOrganisationId(request.getOrganisationId());
        existing.setSubmittedByUserId(request.getSubmittedByUserId());
        existing.setStatus(request.getStatus());
        existing.setEtpDetails(mapToEtpDetails(request.getEtpDetails()));
        existing.getAdditionalInformationRequired().clear();
        for (AdditionalInformationRequest air : request.getAdditionalInformationRequired()) {
            AdditionalInformation ai = new AdditionalInformation();
            ai.setCriteria(air.getCriteria());
            ai.setDetailsOfAdditionalInformationRequired(air.getDetailsOfAdditionalInformationRequired());
            ai.setProvided(air.isProvided());
            ai.setRequestForAdditionalInformation(existing);
            existing.getAdditionalInformationRequired().add(ai);
        }
    }
}