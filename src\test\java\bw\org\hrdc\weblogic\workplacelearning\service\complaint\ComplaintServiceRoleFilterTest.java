package bw.org.hrdc.weblogic.workplacelearning.service.complaint;

import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintSearchCriteria;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ListData;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintRepository;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Test class for ComplaintService role-based filtering functionality
 * Tests the updated logic where agents can see both SUBMITTED and UNDER_REVIEW complaints
 */
@ExtendWith(MockitoExtension.class)
class ComplaintServiceRoleFilterTest {

    @Mock
    private ComplaintRepository complaintRepository;

    @InjectMocks
    private ComplaintService complaintService;

    private ComplaintEntity submittedComplaint;
    private ComplaintEntity underReviewComplaint;
    private ComplaintEntity escalatedComplaint;
    private String agentUserId = "agent-123";
    private String agentLeadUserId = "agent-lead-456";

    @BeforeEach
    void setUp() {
        // Create test complaints with different states
        submittedComplaint = createTestComplaint("complaint-1", Enums.ComplaintState.SUBMITTED, agentUserId);
        underReviewComplaint = createTestComplaint("complaint-2", Enums.ComplaintState.UNDER_REVIEW, agentUserId);
        escalatedComplaint = createTestComplaint("complaint-3", Enums.ComplaintState.ESCALATED, agentLeadUserId);
    }

    @Test
    void testAgentWithUserIdSeesSubmittedAndUnderReviewComplaints() {
        // Arrange
        List<ComplaintEntity> expectedComplaints = Arrays.asList(submittedComplaint, underReviewComplaint);
        Page<ComplaintEntity> mockPage = new PageImpl<>(expectedComplaints);
        
        when(complaintRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(mockPage);

        ComplaintSearchCriteria searchCriteria = new ComplaintSearchCriteria();
        searchCriteria.setRole("AGENT");
        searchCriteria.setAssignedTo(agentUserId);

        // Act
        Page<ListData> result = complaintService.fetchUserAssignedList(PageRequest.of(0, 10), searchCriteria);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        
        // Verify that both SUBMITTED and UNDER_REVIEW complaints are returned
        List<String> returnedStates = result.getContent().stream()
                .map(ListData::getState)
                .toList();
        
        assertTrue(returnedStates.contains("SUBMITTED"));
        assertTrue(returnedStates.contains("UNDER_REVIEW"));
    }

    @Test
    void testAgentWithoutUserIdSeesAllSubmittedAndUnderReviewComplaints() {
        // Arrange
        List<ComplaintEntity> expectedComplaints = Arrays.asList(submittedComplaint, underReviewComplaint);
        Page<ComplaintEntity> mockPage = new PageImpl<>(expectedComplaints);
        
        when(complaintRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(mockPage);

        ComplaintSearchCriteria searchCriteria = new ComplaintSearchCriteria();
        searchCriteria.setRole("AGENT");
        // No assignedTo (userId) set

        // Act
        Page<ListData> result = complaintService.fetchUserAssignedList(PageRequest.of(0, 10), searchCriteria);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        
        // Verify that both SUBMITTED and UNDER_REVIEW complaints are returned
        List<String> returnedStates = result.getContent().stream()
                .map(ListData::getState)
                .toList();
        
        assertTrue(returnedStates.contains("SUBMITTED"));
        assertTrue(returnedStates.contains("UNDER_REVIEW"));
    }

    @Test
    void testAgentLeadSeesOnlyEscalatedComplaints() {
        // Arrange
        List<ComplaintEntity> expectedComplaints = Arrays.asList(escalatedComplaint);
        Page<ComplaintEntity> mockPage = new PageImpl<>(expectedComplaints);
        
        when(complaintRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(mockPage);

        ComplaintSearchCriteria searchCriteria = new ComplaintSearchCriteria();
        searchCriteria.setRole("AGENT_LEAD");
        searchCriteria.setAssignedTo(agentLeadUserId);

        // Act
        Page<ListData> result = complaintService.fetchUserAssignedList(PageRequest.of(0, 10), searchCriteria);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals("ESCALATED", result.getContent().get(0).getState());
    }

    private ComplaintEntity createTestComplaint(String uuid, Enums.ComplaintState state, String assignee) {
        ComplaintEntity complaint = new ComplaintEntity();
        complaint.setUuid(uuid);
        complaint.setState(state);
        complaint.setStatus(Enums.ComplaintStatus.OPEN);
        complaint.setAssignee(assignee);
        complaint.setCategory(Enums.CategoryComplaint.COMPLAINT);
        complaint.setReferenceNumber("CMP-2024-" + uuid);
        complaint.setTypeOfComplaint("Test Complaint");
        complaint.setDepartment(Enums.Department.GENERAL);
        complaint.setCreatedAt(LocalDateTime.now());
        complaint.setIsDeleted(false);
        return complaint;
    }
}
