package bw.org.hrdc.weblogic.workplacelearning.service.complaint;

import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintSearchCriteria;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ListData;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * Test class for ComplaintService role-based filtering functionality
 * Tests the updated logic where agents can see OPEN SUBMITTED and OPEN UNDER_REVIEW complaints
 */
@ExtendWith(MockitoExtension.class)
class ComplaintServiceRoleFilterTest {

    @Mock
    private ComplaintRepository complaintRepository;

    @InjectMocks
    private ComplaintService complaintService;

    @Test
    void testAgentRoleBasedFilteringCallsRepository() {
        // Arrange
        Page<ComplaintEntity> mockPage = new PageImpl<>(Collections.emptyList());
        
        when(complaintRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(mockPage);

        ComplaintSearchCriteria searchCriteria = new ComplaintSearchCriteria();
        searchCriteria.setRole("AGENT");
        searchCriteria.setAssignedTo("agent-123");

        // Act
        Page<ListData> result = complaintService.fetchUserAssignedList(PageRequest.of(0, 10), searchCriteria);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
    }

    @Test
    void testAgentRoleBasedFilteringWithoutUserId() {
        // Arrange
        Page<ComplaintEntity> mockPage = new PageImpl<>(Collections.emptyList());
        
        when(complaintRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(mockPage);

        ComplaintSearchCriteria searchCriteria = new ComplaintSearchCriteria();
        searchCriteria.setRole("AGENT");
        // No assignedTo set

        // Act
        Page<ListData> result = complaintService.fetchUserAssignedList(PageRequest.of(0, 10), searchCriteria);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
    }
}
