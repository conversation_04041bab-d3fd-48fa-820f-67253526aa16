package bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.UUID;

/**
 * DTO for managing Short Course Information.
 */
@Data
public class RFCShortCourseInformationDto {

    private UUID uuid;

    @NotNull(message = "Title cannot be null")
    @Schema(description = "The title of the short course", example = "Introduction to Programming")
    private String title;

    @NotNull(message = "Type cannot be null")
    @Schema(description = "The type of the short course", example = "Workshop")
    private String type;

    @Schema(description = "The field of learning", example = "Computer Science")
    private String fieldOfLearning;

    @Schema(description = "The total learning time for the course", example = "40")
    private int courseLearningTime;

    @Schema(description = "The duration of the course", example = "2 weeks")
    private String duration;

    @Schema(description = "The year due for review", example = "2025")
    private String yearDueForReview;

    @Schema(description = "The target population for the course", example = "University students")
    private String targetPopulation;

    @Schema(description = "The entry requirements for the course", example = "Basic computer literacy")
    private String entryRequirements;
}
