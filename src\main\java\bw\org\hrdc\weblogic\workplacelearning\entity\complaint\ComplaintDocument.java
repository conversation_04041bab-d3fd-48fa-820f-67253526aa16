package bw.org.hrdc.weblogic.workplacelearning.entity.complaint;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Base;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @CreatedOn 27/03/25 21:35
 * @UpdatedBy martinspectre
 * @UpdatedOn 27/03/25 21:35
 */
@Entity
@Table(name = "complaint_documents")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ComplaintDocument extends Base implements Serializable {

    @ManyToOne
    @JoinColumn(name = "complaint_id", nullable = false)
    @JsonIgnore
    private ComplaintEntity complaint;

    private String key;
    private String docName;
    private String docExt;
    private String identifier;
    private String fileType;
    private Integer docSize;
    private String fileUrl;
}

