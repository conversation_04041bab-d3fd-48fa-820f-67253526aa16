package bw.org.hrdc.weblogic.workplacelearning.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for managing Non-Credit Bearing Course details.
 */
@Data
@Schema(
        name = "NonCreditBearingCourse",
        description = "Schema to hold Non-Credit Bearing Course details"
)
public class NonCreditBearingCourseDto {

    @Schema(description = "Unique identifier for the course", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @NotNull(message = "Title cannot be null")
    @Schema(description = "The title of the course", example = "Fundamentals of Data Analysis")
    private String title;

    @NotNull(message = "Type cannot be null")
    @Schema(description = "The type of the course", example = "Short Course")
    private String type;

    @NotNull(message = "Field of learning cannot be null")
    @Schema(description = "The field of learning for the course", example = "Data Science")
    private String fieldOfLearning;

    @NotNull(message = "Learning time in hours cannot be null")
    @Schema(description = "The total learning time for the course in hours", example = "40")
    private int learningTimeInHours;

    @NotNull(message = "Date developed cannot be null")
    @Schema(description = "The date the course was developed", example = "2023-10-15")
    private LocalDate dateDeveloped;

    @NotNull(message = "Target population cannot be null")
    @Schema(description = "The target population for the course", example = "100")
    private int targetPopulation;

    @NotNull(message = "Entry requirements cannot be null")
    @Schema(description = "The entry requirements for the course", example = "Basic knowledge in programming")
    private String entryRequirements;

    @NotNull(message = "Organisation ID cannot be null")
    @Schema(description = "The ID of the organization offering the course", example = "c3d4f50b-58cc-4372-a567-0e02b2c3d234")
    private UUID organisationId;

    @Schema(description = "Timestamp when the course was created", example = "2024-11-26T10:00:00Z")
    private LocalDateTime createdAt;

    @Schema(description = "User who created the course", example = "<EMAIL>")
    private String createdBy;

    @Schema(description = "Timestamp when the course was last updated", example = "2024-11-28T14:00:00Z")
    private LocalDateTime updatedAt;

    @Schema(description = "User who last updated the course", example = "<EMAIL>")
    private String updatedBy;
}
