package bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.CourseContentDelivery;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.CourseDeliverySchedule;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ScopeOfAccreditation;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ShortCourseInformation;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @CreatedOn 08/04/25 08:30
 * @UpdatedBy martinspectre
 * @UpdatedOn 08/04/25 08:30
 */
@Data
public class RecognitionUpdateDto {
    private Long id;
    private String uuid;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;
    private boolean isDeleted;
    private String assignedAgent;
    private String assignedAgentLead;
    private String assignedOfficerLead;
    private String assignedOfficer;
    private String assignedManager;
    private String referenceNumber;
    private String applicationNumber;
    private String organisationId;
    private String trainingNeedsAssessmentPurpose;
    private String trainingNeedsAssessmentSkillsNeedsAnalysis;
    private String shortCourseDeliveryMode;
    private String keyFacilitation;
    private String assessmentType;
    private String certification;
    private String thirdPartyArrangements;
    private String resources;
    private String shortCourseEndorsement;
    private LocalDateTime dateSubmitted;
    private Enums.Status applicationStatus;
    private Enums.State applicationState;
    private ShortCourseInformation shortCourseInformation;
    private CourseContentDelivery courseContentAndDelivery;
    private Set<ScopeOfAccreditation> scopeOfAccreditation;
    private Set<CourseDeliverySchedule> courseDeliverySchedule;
    private Set<LearningOutcomePayloadDto> learningOutcomes;
}
