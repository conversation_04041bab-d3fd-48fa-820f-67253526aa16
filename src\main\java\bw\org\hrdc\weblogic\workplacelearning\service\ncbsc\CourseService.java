package bw.org.hrdc.weblogic.workplacelearning.service.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.*;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition.AssessmentCriteriaRepo;
import bw.org.hrdc.weblogic.workplacelearning.service.IShortCourseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CourseService {

    @Autowired
    private IShortCourseRepository courseInformationRepo;
    @Autowired
    private AssessmentCriteriaRepo assessmentCriteriaRepo;

    public List<ShortCourseInformationListDto> getShortCoursesByOrganisation(String organisationId) {
        return courseInformationRepo.findShortCoursesByOrganisationId(organisationId);
    }

    public List<AssessmentCriteriaExtendedDto> getCourseTopics(String courseId) {
        return assessmentCriteriaRepo.findByCourseId(courseId)
                .stream()
                .map(ac -> new AssessmentCriteriaExtendedDto(
                        ac.getUuid(),
                        ac.getTopic(),
                        ac.getObjective(),
                        ac.getDeliveryStrategy(),
                        ac.getAssessmentStrategy()
                )).collect(Collectors.toList());
    }

}
