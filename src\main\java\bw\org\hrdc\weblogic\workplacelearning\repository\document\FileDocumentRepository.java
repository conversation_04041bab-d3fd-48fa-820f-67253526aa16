package bw.org.hrdc.weblogic.workplacelearning.repository.document;

import bw.org.hrdc.weblogic.workplacelearning.entity.document.FileDocument;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface FileDocumentRepository extends JpaRepository<FileDocument, UUID> {

    List<FileDocument> findByIdAndEntityType(UUID id, Enums.FileEntityType entityType);

    List<FileDocument> findByEntityType(Enums.FileEntityType entityType);

    void deleteByIdAndEntityType(UUID entityId, Enums.FileEntityType entityType);

    @Transactional
    @Modifying
    @Query("UPDATE FileDocument f SET f.docName = :docName WHERE f.id = :id")
    int updateDocNameById(@Param("id") UUID id, @Param("docName") String docName);
}
