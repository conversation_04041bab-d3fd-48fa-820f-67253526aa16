package bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.CourseDetails;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for WorkPlaceTrainingPlan entity.
 */
@Repository
public interface WorkPlaceTrainingPlanRepository
        extends JpaRepository<WorkPlaceTrainingPlan, UUID>, JpaSpecificationExecutor<WorkPlaceTrainingPlan>, 
                WorkPlaceTrainingPlanRepositoryCustom {

    /**
     * Find training plans by their organisation ID.
     *
     * @param organisationId the organisation ID.
     * @return a list of training plans belonging to the given organisation.
     */
    List<WorkPlaceTrainingPlan> findByOrganisationId(UUID organisationId);

    /**
     * Find training plans by user ID.
     *
     * @param userId the user ID.
     * @return a list of training plans created by the given user.
     */
    List<WorkPlaceTrainingPlan> findByUserId(UUID userId);

    /**
     * Find training plans by their application status.
     *
     * @param applicationStatus the application status.
     * @return a list of training plans with the given status.
     */
    List<WorkPlaceTrainingPlan> findByApplicationStatus(String applicationStatus);

    /**
     * Find an application by its reference number.
     *
     * @param referenceNumber the reference number of the application
     * @return Optional containing the application if found
     */
    Optional<WorkPlaceTrainingPlan> findByReferenceNumber(String referenceNumber);

    @Modifying
    @Transactional
    @Query("UPDATE WorkPlaceTrainingPlan t SET " +
            "t.lastModifiedDate = CURRENT_TIMESTAMP, " +
            "t.assignedAgent = CASE WHEN :role = 'AGENT' THEN :userId ELSE t.assignedAgent END, " +
            "t.assignedAgentLead = CASE WHEN :role = 'AGENT_LEAD' THEN :userId ELSE t.assignedAgentLead END, " +
            "t.assignedOfficer = CASE WHEN :role = 'OFFICER' THEN :userId ELSE t.assignedOfficer END, " +
            "t.assignedOfficerLead = CASE WHEN :role = 'OFFICER_LEAD' THEN :userId ELSE t.assignedOfficerLead END, " +
            "t.assignedManager = CASE WHEN :role = 'MANAGER' THEN :userId ELSE t.assignedManager END, " +
            "t.applicationState = " +
            "   CASE " +
            "       WHEN :role = 'AGENT' THEN 'IN_PROCESSING' " +
            "       WHEN :role = 'AGENT_LEAD' THEN 'SUBMITTED' " +
            "       WHEN :role = 'OFFICER' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'OFFICER_LEAD' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'MANAGER' THEN 'IN_APPROVAL' " +
            "       ELSE t.applicationState " +
            "   END " +
            "WHERE t.id = :id")
    int updateTrainingPlanAssignedUser(
            @Param("id") UUID id,
            @Param("role") String role,
            @Param("userId") String userId);

    @Modifying
    @Transactional
    @Query("UPDATE WorkPlaceTrainingPlan t " +
            "SET t.assignedOfficerLead = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN :newAssignee " +
            "    ELSE t.assignedOfficerLead " +
            "END, " +
            "t.applicationState = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN 'IN_REVIEW' " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN 'IN_APPROVAL' " +
            "    ELSE t.applicationState " +
            "END, " +
            "t.applicationStatus = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN 'PENDING' " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN 'PENDING' " +
            "    ELSE :action " +
            "END, " +
            "t.assignedManager = CASE " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN :newAssignee " +
            "    ELSE t.assignedManager " +
            "END, " +
            "t.assignedAgent = CASE " +
            "    WHEN :role = 'OFFICER' AND :action = 'CHANGE_REQUEST' THEN :newAssignee " +
            "    ELSE t.assignedAgent " +
            "END, " +
            "t.lastModifiedDate = CURRENT_TIMESTAMP " +
            "WHERE t.id = :trainingPlanId")
    int changeTrainingPlanStatus(
            @Param("trainingPlanId") UUID trainingPlanId,
            @Param("role") String role,
            @Param("action") String action,
            @Param("newAssignee") String newAssignee);

}
