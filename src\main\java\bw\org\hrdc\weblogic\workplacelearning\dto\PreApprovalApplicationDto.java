package bw.org.hrdc.weblogic.workplacelearning.dto;

import bw.org.hrdc.weblogic.workplacelearning.config.JsonStringListDeserializer;
import bw.org.hrdc.weblogic.workplacelearning.dto.document.FileDocumentDto;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * DTO for PreApprovalApplication.
 */
@Data
@Schema(
        name = "PreApprovalApplication",
        description = "Schema to hold Pre-Approval Application details"
)
public class PreApprovalApplicationDto {

    @Schema(description = "Unique identifier for the pre-approval application", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @Schema(description = "Unique identifier for the user submitting the application", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID userId;

    @NotNull(message = "Organization ID cannot be null")
    @Schema(description = "Unique identifier for the organization", example = "f7h8j90b-58cc-4372-a567-0e02b2c3d234")
    private UUID organisationId;

    @Schema(description = "Company details from account service (includes employees, contact person, and registration details)")
    private Object company;

    @Schema(description = "Status of the application", example = "INITIAL")
    private String status;

    @Schema(description = "State of the application", example = "DRAFT")
    private String state;

    @Schema(description = "Application number", example = "APP-********-ABC123")
    private String applicationNumber;

    @Schema(description = "Reference number", example = "REF-********-XYZ789")
    private String referenceNumber;

    @Schema(description = "Reason for training", example = "To upskill employees for advanced tasks.")
    private String reasonForTraining;

    @Schema(description = "Title of the training course", example = "Advanced Data Science")
    private String courseTitle;

    @Schema(description = "Training provider", example = "ABC Training Institute")
    private String trainingProvider;

    @Schema(description = "City where training will take place", example = "Gaborone")
    private String cityOfTraining;

    @Schema(description = "Start date of training", example = "2024-01-01")
    private Date trainingStartDate;

    @Schema(description = "End date of training", example = "2024-03-01")
    private Date trainingEndDate;

    @Schema(description = "List of training details")
    private List<ParticularOfTrainingDto> particularOfTrainings;

    @Schema(description = "List of module details")
    private List<ModuleDetailsDto> moduleDetails;

    @Schema(description = "Estimated training costs")
    private List<EstimatedTrainingCostsDto> estimatedTrainingCosts;

    @Schema(description = "Subtotal of all training costs", example = "1000.00")
    private BigDecimal subtotal;

    @Schema(description = "Total training cost including VAT and other fees", example = "1150.00")
    private BigDecimal total;

    @Schema(description = "VAT number", example = "BW123456")
    private String vatNumber;

    @Schema(description = "Accreditation evidence", example = "Certificate of Accreditation")
    private String accreditationEvidence;

    @Schema(description = "List of employee IDs", example = "[\"uuid1\", \"uuid2\"]")
    @JsonDeserialize(using = JsonStringListDeserializer.class)
    private List<String> employeeIds;

    // Role-based workflow fields
    @Schema(description = "Username of the assigned agent", example = "agent.john")
    private String assignedAgent;

    @Schema(description = "Username of the assigned agent lead", example = "agentlead.mary")
    private String assignedAgentLead;

    @Schema(description = "Username of the assigned officer lead", example = "officerlead.sarah")
    private String assignedOfficerLead;

    @Schema(description = "Username of the assigned officer", example = "officer.david")
    private String assignedOfficer;

    @Schema(description = "Username of the assigned manager", example = "manager.james")
    private String assignedManager;

    // Audit fields
    @Schema(description = "Created date", example = "2024-12-01")
    private Date createdDate;

    @Schema(description = "Last modified date", example = "2024-12-20")
    private Date lastModifiedDate;

    @Schema(description = "Created by", example = "<EMAIL>")
    private String createdBy;

    @Schema(description = "Last modified by", example = "<EMAIL>")
    private String lastModifiedBy;

    @Schema(description = "Total learning hours for all modules", example = "120")
    private String totalLearningHours;
           
    private List<FileDocumentDto> attachments;

    @Schema(description = "Paperless ID for document management system", example = "789012")
    private String paperlessId;
}
