package bw.org.hrdc.weblogic.workplacelearning.mapper;

import bw.org.hrdc.weblogic.workplacelearning.dto.BatchDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.Batch;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between Batch entity and BatchDto.
 */
@Component
public class BatchMapper {

    private final ModelMapper modelMapper;

    @Autowired
    public BatchMapper(ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
    }

    // Convert Entity to DTO
    public BatchDto toDto(Batch entity) {
        return modelMapper.map(entity, BatchDto.class);
    }

    // Convert DTO to Entity
    public Batch toEntity(BatchDto dto) {
        return modelMapper.map(dto, Batch.class);
    }

    // Convert Page<Batch> to Page<BatchDto>
    public Page<BatchDto> toDtoPage(Page<Batch> entityPage) {
        return entityPage.map(this::toDto);
    }

    // Convert List<Batch> to List<BatchDto>
    public List<BatchDto> toDtoList(List<Batch> entityList) {
        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
