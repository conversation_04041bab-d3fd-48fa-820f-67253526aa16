package bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc;

import jakarta.persistence.Column;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @CreatedOn 18/02/25 20:05
 * @UpdatedBy martinspectre
 * @UpdatedOn 18/02/25 20:05
 */
@Data
public class ShortCourseInformationDto{
    private String uuid;
    private String title;
    private String duration;
    private String type;
    private String fieldOfLearning;
    private int courseLearningTime;
    private Date startDate;
    private Date endDate;
    private String yearDueForReview;
    private String targetPopulation;
    private String entryRequirements;
    private String subFieldOfLearning;
    private String level;
    private String accreditingBody;
}
